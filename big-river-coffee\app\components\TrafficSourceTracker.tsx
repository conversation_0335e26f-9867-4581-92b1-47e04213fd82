import { useEffect } from 'react';
import { useUTMTracking } from '~/hooks/useUTMTracking';

/**
 * Traffic Source Tracker Component
 * Specifically designed to track users coming from social media ads and other campaigns
 */
export function TrafficSourceTracker() {
  const { utmParams, hasUTM } = useUTMTracking();

  useEffect(() => {
    if (hasUTM && typeof window !== 'undefined') {
      // Send traffic source data to Google Analytics
      if (window.gtag) {
        // Send custom event for traffic source tracking
        window.gtag('event', 'traffic_source_identified', {
          event_category: 'Traffic Attribution',
          event_label: `${utmParams.utm_source} / ${utmParams.utm_medium}`,
          traffic_source: utmParams.utm_source,
          traffic_medium: utmParams.utm_medium,
          traffic_campaign: utmParams.utm_campaign,
          traffic_content: utmParams.utm_content,
          traffic_term: utmParams.utm_term,
          custom_parameter_1: utmParams.utm_source, // For custom reporting
          custom_parameter_2: utmParams.utm_medium,
        });

        // Enhanced logging for social media traffic
        if (isSocialMediaTraffic(utmParams.utm_source, utmParams.utm_medium)) {
          console.log('🎯 SOCIAL MEDIA TRAFFIC DETECTED:', {
            platform: utmParams.utm_source,
            campaign: utmParams.utm_campaign,
            content: utmParams.utm_content,
            timestamp: new Date().toISOString(),
            url: window.location.href
          });

          // Send specific social media tracking event
          window.gtag('event', 'social_media_traffic', {
            event_category: 'Social Media Attribution',
            event_label: utmParams.utm_source,
            social_platform: utmParams.utm_source,
            social_campaign: utmParams.utm_campaign,
            social_content: utmParams.utm_content,
          });
        }

        // Enhanced logging for paid advertising
        if (isPaidAdvertising(utmParams.utm_medium)) {
          console.log('💰 PAID ADVERTISING TRAFFIC DETECTED:', {
            source: utmParams.utm_source,
            medium: utmParams.utm_medium,
            campaign: utmParams.utm_campaign,
            term: utmParams.utm_term,
            timestamp: new Date().toISOString()
          });

          // Send paid advertising tracking event
          window.gtag('event', 'paid_advertising_traffic', {
            event_category: 'Paid Advertising Attribution',
            event_label: `${utmParams.utm_source} - ${utmParams.utm_campaign}`,
            ad_source: utmParams.utm_source,
            ad_medium: utmParams.utm_medium,
            ad_campaign: utmParams.utm_campaign,
            ad_term: utmParams.utm_term,
          });
        }
      }

      // Log all traffic sources for debugging
      console.log('📊 TRAFFIC SOURCE ATTRIBUTION:', {
        source: utmParams.utm_source,
        medium: utmParams.utm_medium,
        campaign: utmParams.utm_campaign,
        content: utmParams.utm_content,
        term: utmParams.utm_term,
        type: getTrafficType(utmParams.utm_source, utmParams.utm_medium),
        timestamp: new Date().toISOString(),
        page: window.location.pathname
      });
    }
  }, [hasUTM, utmParams]);

  return null; // This component doesn't render anything
}

/**
 * Helper function to identify social media traffic
 */
function isSocialMediaTraffic(source?: string, medium?: string): boolean {
  if (!source && !medium) return false;
  
  const socialSources = [
    'facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 
    'snapchat', 'pinterest', 'youtube', 'reddit'
  ];
  
  const socialMediums = ['social', 'social-media', 'social_media'];
  
  return socialSources.includes(source?.toLowerCase() || '') || 
         socialMediums.includes(medium?.toLowerCase() || '');
}

/**
 * Helper function to identify paid advertising
 */
function isPaidAdvertising(medium?: string): boolean {
  if (!medium) return false;
  
  const paidMediums = [
    'cpc', 'ppc', 'paid', 'ads', 'advertising', 
    'paid-social', 'paid_social', 'display', 'banner'
  ];
  
  return paidMediums.includes(medium.toLowerCase());
}

/**
 * Helper function to categorize traffic type
 */
function getTrafficType(source?: string, medium?: string): string {
  if (isSocialMediaTraffic(source, medium)) return 'Social Media';
  if (isPaidAdvertising(medium)) return 'Paid Advertising';
  if (medium === 'email') return 'Email Marketing';
  if (medium === 'referral') return 'Referral';
  if (medium === 'organic') return 'Organic Search';
  return 'Other';
}

/**
 * Export helper functions for use in other components
 */
export { isSocialMediaTraffic, isPaidAdvertising, getTrafficType };
