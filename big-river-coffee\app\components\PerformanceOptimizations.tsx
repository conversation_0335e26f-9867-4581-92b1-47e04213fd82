import { useEffect } from 'react';

/**
 * Performance optimizations component
 * Implements various performance improvements for better Core Web Vitals
 */
export function PerformanceOptimizations() {
  useEffect(() => {
    // Preload critical images when the page loads - mobile optimized
    const preloadCriticalImages = () => {
      const isMobile = window.innerWidth <= 768;

      // Different critical images for mobile vs desktop
      const criticalImages = isMobile ? [
        '/headerlogo.svg',
        '/newhomepage/mobile_homeage_bg_sf.webp',
        '/mobilepopup.webp'
      ] : [
        '/headerlogo.svg',
        '/hpheronew.svg',
        '/newhomepage/bg_video/bg_sf_new.webp',
        '/brewedforwild.webp'
      ];

      criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    };

    // Optimize images by adding loading attributes to existing images
    const optimizeExistingImages = () => {
      const images = document.querySelectorAll('img:not([loading])');
      const isMobile = window.innerWidth <= 768;

      images.forEach((img, index) => {
        // On mobile, be more aggressive with lazy loading
        const eagerThreshold = isMobile ? 1 : 3;

        // Check if image is above the fold
        const rect = img.getBoundingClientRect();
        const isAboveFold = rect.top < window.innerHeight;

        if (index < eagerThreshold && isAboveFold) {
          img.setAttribute('loading', 'eager');
        } else {
          img.setAttribute('loading', 'lazy');
        }

        // Add decoding optimization
        img.setAttribute('decoding', 'async');
      });
    };

    // Add intersection observer for lazy loading fallback
    const addIntersectionObserver = () => {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        });

        // Observe images with data-src attribute
        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }
    };

    // Optimize video loading and add viewport-based play/pause
    const optimizeVideos = () => {
      const videos = document.querySelectorAll('video');
      const isMobile = window.innerWidth <= 768;

      videos.forEach(video => {
        // On mobile, be more conservative with video loading
        if (isMobile) {
          video.setAttribute('preload', 'none');
          // Pause any playing videos on mobile to save bandwidth
          if (!video.paused) {
            video.pause();
          }
        } else {
          // Add preload="metadata" for better performance on desktop
          if (!video.hasAttribute('preload')) {
            video.setAttribute('preload', 'metadata');
          }
        }

        // Add loading="lazy" if supported
        if ('loading' in HTMLVideoElement.prototype) {
          video.setAttribute('loading', 'lazy');
        }

        // Add intersection observer for play/pause based on viewport
        if (!video.hasAttribute('data-optimized')) {
          const observer = new IntersectionObserver(
            (entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting) {
                  // Video is in viewport, play if it should autoplay
                  if (video.hasAttribute('autoplay')) {
                    video.play().catch(() => {
                      // Ignore autoplay failures
                    });
                  }
                } else {
                  // Video is out of viewport, pause to save bandwidth
                  video.pause();
                }
              });
            },
            {
              threshold: 0.3, // Pause when less than 30% visible
              rootMargin: '50px', // Start playing 50px before entering viewport
            }
          );

          observer.observe(video);
          video.setAttribute('data-optimized', 'true');
        }
      });
    };

    // Defer non-critical JavaScript
    const deferNonCriticalJS = () => {
      // Add defer attribute to non-critical scripts
      const scripts = document.querySelectorAll('script[src]:not([defer]):not([async])');
      scripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src && !src.includes('gtag') && !src.includes('analytics')) {
          script.setAttribute('defer', '');
        }
      });
    };

    // Optimize font loading
    const optimizeFonts = () => {
      // Add font-display: swap to improve text rendering
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-display: swap;
        }
      `;
      document.head.appendChild(style);
    };

    // Mobile-specific performance optimizations
    const optimizeForMobile = () => {
      const isMobile = window.innerWidth <= 768;
      if (!isMobile) return;

      // Reduce animation complexity on mobile
      const style = document.createElement('style');
      style.textContent = `
        @media (max-width: 768px) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }

          /* Disable expensive CSS effects on mobile */
          .transition-all,
          .transition-opacity,
          .transition-transform {
            transition: none !important;
          }
        }
      `;
      document.head.appendChild(style);

      // Disable hover effects on mobile
      document.body.classList.add('mobile-device');
    };

    // Run optimizations
    preloadCriticalImages();
    optimizeExistingImages();
    addIntersectionObserver();
    optimizeVideos();
    deferNonCriticalJS();
    optimizeFonts();
    optimizeForMobile();

    // Cleanup function
    return () => {
      // Remove any event listeners if needed
    };
  }, []);

  return null; // This component doesn't render anything
}

/**
 * Critical Resource Preloader
 * Preloads the most important resources for faster page loads
 */
export function CriticalResourcePreloader() {
  useEffect(() => {
    // Preload critical CSS
    const preloadCSS = (href: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    };

    // Preload critical fonts (if any)
    const preloadFonts = () => {
      // Add any critical fonts here
      const criticalFonts: string[] = [
        // '/fonts/your-critical-font.woff2'
      ];

      criticalFonts.forEach((font: string) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = font;
        document.head.appendChild(link);
      });
    };

    preloadFonts();
  }, []);

  return null;
}

/**
 * Service Worker Registration
 * Registers a service worker for caching and offline functionality
 */
export function ServiceWorkerRegistration() {
  useEffect(() => {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  }, []);

  return null;
}
