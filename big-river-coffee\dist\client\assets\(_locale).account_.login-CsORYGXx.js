import{w as l}from"./with-props-CE_bzIRz.js";import{j as e}from"./jsx-runtime-CWqDQG74.js";import{e as c,a as d,F as n,L as m}from"./chunk-D4RADZKF-CZTShXQu.js";const p=()=>[{title:"Login | Big River Coffee"}],f=l(function(){var a,o;const s=c(),t=d(),i=t.state==="submitting"&&((a=t.formData)==null?void 0:a.get("_action"))!=="guest",r=t.state==="submitting"&&((o=t.formData)==null?void 0:o.get("_action"))==="guest";return e.jsx("div",{className:"min-h-screen",style:{backgroundColor:"#f97316"},children:e.jsx("div",{className:"max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsx("div",{className:"bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100",children:e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("span",{className:"text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4",children:"Account Access"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600",children:"Sign in to your account or continue as guest"})]}),(s==null?void 0:s.error)&&e.jsx("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:s.error==="authorization_failed"?"Authorization failed. Please try again.":"An error occurred. Please try again."}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(n,{method:"post",className:"space-y-4",children:e.jsx("div",{children:e.jsxs("button",{type:"submit",disabled:i,className:"group relative flex w-full justify-center rounded-md bg-army-600 px-4 py-3.5 text-sm font-semibold text-white hover:bg-army-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:i?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"h-5 w-5 text-white group-hover:text-gray-100",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),i?"Signing in...":"Sign in to your account"]})})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"or"})})]}),e.jsxs(n,{method:"post",children:[e.jsx("input",{type:"hidden",name:"_action",value:"guest"}),e.jsxs("button",{type:"submit",disabled:r,className:"group relative flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-3.5 text-sm font-semibold text-gray-700 hover:bg-gray-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:r?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-gray-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"h-5 w-5 text-gray-500 group-hover:text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),r?"Continuing...":"Continue as guest"]})]})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["New to Big River Coffee?"," ",e.jsx(m,{to:"/account/register",className:"font-medium text-army-600 hover:text-army-700 transition-colors duration-200",children:"Create an account"})]})})]})})})})});export{f as default,p as meta};
