# Deployment Issues & Fixes

## Issue 1: 403 Permissions Error

### Problem
Users getting 403 forbidden error when accessing www.bigrivercoffee.com, but you can access it from your laptop.

### Root Cause
Your Oxygen environment is set to **private** instead of **public**. Private environments require Shopify authentication to view.

### Solution
1. Go to your **Shopify Admin**
2. Navigate to **Sales channels** → **Hydrogen**
3. Select your **Big River Coffee** storefront
4. Click the `…` beside your **production environment**
5. Click **Edit environment**
6. Change **URL privacy** from **Private** to **Public**
7. Click **Save**

### Verification
After making this change, test the site from an incognito browser or different device to confirm public access works.

---

## Issue 2: Subscription Management Redirect

### Problem
The subscription management link `https://9bc094.myshopify.com/a/subscriptions/login` is redirecting to `https://www.bigrivercoffee.com/a/subscriptions/login` instead of staying on the myshopify.com domain where the Seal Subscriptions portal exists.

### Root Cause
When you set up a custom domain (www.bigrivercoffee.com) as your primary domain targeting your **Hydrogen storefront**, Shopify automatically redirects ALL traffic from your `9bc094.myshopify.com` domain to your custom domain, including app portal URLs.

### Solution: Fix Domain Target Settings (RECOMMENDED)

**Step-by-Step Fix:**

1. **Go to your Shopify Admin**
2. **Navigate to Settings → Domains**
3. **Look for your domain configuration** - you should see:
   - `www.bigrivercoffee.com` (or `bigrivercoffee.com`)
   - `9bc094.myshopify.com`

4. **Check the current setup** - likely you have:
   - `www.bigrivercoffee.com` → **Target**: Hydrogen storefront → **Type**: Primary
   - `9bc094.myshopify.com` → **Target**: Hydrogen storefront → **Type**: Redirect

5. **Fix the myshopify.com domain:**
   - Click on `9bc094.myshopify.com`
   - Click **Change target**
   - Change **Target** from "Hydrogen storefront" to **"Online Store"**
   - Keep **Type** as "Redirect" (this is fine)
   - Click **Save**

**What this does:**
- Your custom domain continues to point to your Hydrogen storefront
- Your `myshopify.com` domain now points to the Online Store, allowing access to app portals
- Subscription management URLs will work on the myshopify.com domain
- Your main site continues to work normally on the custom domain

### Solution 2: Alternative Bypass Methods (if Solution 1 doesn't work)

If the JavaScript method still gets redirected, try these approaches:

1. **URL with Additional Bypass Parameters:**
   ```
   https://9bc094.myshopify.com/a/subscriptions/login?no_redirect=1&force_domain=myshopify
   ```

2. **Direct IP Access (if DNS redirect):**
   ```
   https://************/a/subscriptions/login
   ```
   (Note: This bypasses DNS-level redirects but may not work for SSL)

3. **Subdomain Approach:**
   ```
   https://admin.9bc094.myshopify.com/a/subscriptions/login
   ```

4. **Alternative Seal Subscriptions URLs:**
   ```
   https://9bc094.myshopify.com/apps/seal-subscriptions/customer-portal
   https://9bc094.myshopify.com/account/subscriptions
   https://9bc094.myshopify.com/tools/recurring/customer-portal
   ```

### Solution 3: Domain Configuration Fix
If the above URLs still redirect, you may need to adjust your domain configuration:

1. Go to **Shopify Admin** → **Settings** → **Domains**
2. Check if you have redirect rules that are too broad
3. Consider setting up a subdomain (e.g., `shop.bigrivercoffee.com`) for the Hydrogen storefront while keeping the main domain for other purposes

### Solution 4: JavaScript Redirect Bypass
If server-side redirects are unavoidable, implement a client-side solution:

```javascript
// Add this to handle subscription management clicks
function openSubscriptionPortal() {
  // Try to bypass redirects by opening in a new window with specific parameters
  const subscriptionUrl = 'https://9bc094.myshopify.com/a/subscriptions/login';
  const newWindow = window.open('', '_blank');
  newWindow.location.href = subscriptionUrl;
}
```

---

## Environment Variables Verification

### Required Variables (Auto-injected by Oxygen)
Verify these are set in your Oxygen environment:

- `SESSION_SECRET` - Required for session management
- `PUBLIC_STORE_DOMAIN` - Your store domain
- `PUBLIC_STOREFRONT_API_TOKEN` - Public API token
- `PRIVATE_STOREFRONT_API_TOKEN` - Private API token
- `PRIVATE_ADMIN_API_TOKEN` - Admin API token (for contact forms)
- `PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID` - Customer account API
- `PUBLIC_CUSTOMER_ACCOUNT_API_URL` - Customer account URL
- `PUBLIC_CHECKOUT_DOMAIN` - Checkout domain

### How to Check
1. Go to **Shopify Admin** → **Sales channels** → **Hydrogen**
2. Select your storefront
3. Click **Storefront settings**
4. Click **Environments and variables**
5. Verify all required variables are present for your production environment

---

## Testing Checklist

After implementing fixes:

### Issue 1 (403 Error)
- [ ] Test site access from incognito browser
- [ ] Test site access from different device/network
- [ ] Verify all pages load correctly for anonymous users

### Issue 2 (Subscription Management)
- [ ] Click subscription management link from account page
- [ ] Verify it opens the correct Seal Subscriptions portal
- [ ] Test customer login flow in subscription portal
- [ ] Verify subscription management functions work

### General Deployment
- [ ] All environment variables are properly set
- [ ] No console errors on homepage
- [ ] Customer account login/logout works
- [ ] Cart functionality works
- [ ] Product pages load correctly
- [ ] Mobile experience works properly

---

## Rollback Plan

If issues persist:

1. **Revert subscription URL change:**
   ```
   href="https://9bc094.myshopify.com/a/subscriptions/login"
   ```

2. **Contact Seal Subscriptions support** for the correct portal URL for custom domain setups

3. **Consider temporary workaround:** Direct customers to manage subscriptions via email/phone until technical issue is resolved
