function Ea(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Vt={exports:{}},W={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nr;function ba(){if(Nr)return W;Nr=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),n=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),u=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),m=Symbol.iterator;function h(g){return g===null||typeof g!="object"?null:(g=m&&g[m]||g["@@iterator"],typeof g=="function"?g:null)}var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,S={};function b(g,P,z){this.props=g,this.context=P,this.refs=S,this.updater=z||v}b.prototype.isReactComponent={},b.prototype.setState=function(g,P){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,P,"setState")},b.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function R(){}R.prototype=b.prototype;function L(g,P,z){this.props=g,this.context=P,this.refs=S,this.updater=z||v}var _=L.prototype=new R;_.constructor=L,w(_,b.prototype),_.isPureReactComponent=!0;var D=Array.isArray,C=Object.prototype.hasOwnProperty,p={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function j(g,P,z){var J,X={},Z=null,se=null;if(P!=null)for(J in P.ref!==void 0&&(se=P.ref),P.key!==void 0&&(Z=""+P.key),P)C.call(P,J)&&!k.hasOwnProperty(J)&&(X[J]=P[J]);var oe=arguments.length-2;if(oe===1)X.children=z;else if(1<oe){for(var ne=Array(oe),te=0;te<oe;te++)ne[te]=arguments[te+2];X.children=ne}if(g&&g.defaultProps)for(J in oe=g.defaultProps,oe)X[J]===void 0&&(X[J]=oe[J]);return{$$typeof:e,type:g,key:Z,ref:se,props:X,_owner:p.current}}function A(g,P){return{$$typeof:e,type:g.type,key:P,ref:g.ref,props:g.props,_owner:g._owner}}function q(g){return typeof g=="object"&&g!==null&&g.$$typeof===e}function ae(g){var P={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(z){return P[z]})}var fe=/\/+/g;function V(g,P){return typeof g=="object"&&g!==null&&g.key!=null?ae(""+g.key):P.toString(36)}function G(g,P,z,J,X){var Z=typeof g;(Z==="undefined"||Z==="boolean")&&(g=null);var se=!1;if(g===null)se=!0;else switch(Z){case"string":case"number":se=!0;break;case"object":switch(g.$$typeof){case e:case t:se=!0}}if(se)return se=g,X=X(se),g=J===""?"."+V(se,0):J,D(X)?(z="",g!=null&&(z=g.replace(fe,"$&/")+"/"),G(X,P,z,"",function(te){return te})):X!=null&&(q(X)&&(X=A(X,z+(!X.key||se&&se.key===X.key?"":(""+X.key).replace(fe,"$&/")+"/")+g)),P.push(X)),1;if(se=0,J=J===""?".":J+":",D(g))for(var oe=0;oe<g.length;oe++){Z=g[oe];var ne=J+V(Z,oe);se+=G(Z,P,z,ne,X)}else if(ne=h(g),typeof ne=="function")for(g=ne.call(g),oe=0;!(Z=g.next()).done;)Z=Z.value,ne=J+V(Z,oe++),se+=G(Z,P,z,ne,X);else if(Z==="object")throw P=String(g),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.");return se}function le(g,P,z){if(g==null)return g;var J=[],X=0;return G(g,J,"","",function(Z){return P.call(z,Z,X++)}),J}function Y(g){if(g._status===-1){var P=g._result;P=P(),P.then(function(z){(g._status===0||g._status===-1)&&(g._status=1,g._result=z)},function(z){(g._status===0||g._status===-1)&&(g._status=2,g._result=z)}),g._status===-1&&(g._status=0,g._result=P)}if(g._status===1)return g._result.default;throw g._result}var Q={current:null},he={transition:null},pe={ReactCurrentDispatcher:Q,ReactCurrentBatchConfig:he,ReactCurrentOwner:p};function de(){throw Error("act(...) is not supported in production builds of React.")}return W.Children={map:le,forEach:function(g,P,z){le(g,function(){P.apply(this,arguments)},z)},count:function(g){var P=0;return le(g,function(){P++}),P},toArray:function(g){return le(g,function(P){return P})||[]},only:function(g){if(!q(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},W.Component=b,W.Fragment=r,W.Profiler=n,W.PureComponent=L,W.StrictMode=a,W.Suspense=i,W.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=pe,W.act=de,W.cloneElement=function(g,P,z){if(g==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+g+".");var J=w({},g.props),X=g.key,Z=g.ref,se=g._owner;if(P!=null){if(P.ref!==void 0&&(Z=P.ref,se=p.current),P.key!==void 0&&(X=""+P.key),g.type&&g.type.defaultProps)var oe=g.type.defaultProps;for(ne in P)C.call(P,ne)&&!k.hasOwnProperty(ne)&&(J[ne]=P[ne]===void 0&&oe!==void 0?oe[ne]:P[ne])}var ne=arguments.length-2;if(ne===1)J.children=z;else if(1<ne){oe=Array(ne);for(var te=0;te<ne;te++)oe[te]=arguments[te+2];J.children=oe}return{$$typeof:e,type:g.type,key:X,ref:Z,props:J,_owner:se}},W.createContext=function(g){return g={$$typeof:l,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},g.Provider={$$typeof:o,_context:g},g.Consumer=g},W.createElement=j,W.createFactory=function(g){var P=j.bind(null,g);return P.type=g,P},W.createRef=function(){return{current:null}},W.forwardRef=function(g){return{$$typeof:s,render:g}},W.isValidElement=q,W.lazy=function(g){return{$$typeof:d,_payload:{_status:-1,_result:g},_init:Y}},W.memo=function(g,P){return{$$typeof:u,type:g,compare:P===void 0?null:P}},W.startTransition=function(g){var P=he.transition;he.transition={};try{g()}finally{he.transition=P}},W.unstable_act=de,W.useCallback=function(g,P){return Q.current.useCallback(g,P)},W.useContext=function(g){return Q.current.useContext(g)},W.useDebugValue=function(){},W.useDeferredValue=function(g){return Q.current.useDeferredValue(g)},W.useEffect=function(g,P){return Q.current.useEffect(g,P)},W.useId=function(){return Q.current.useId()},W.useImperativeHandle=function(g,P,z){return Q.current.useImperativeHandle(g,P,z)},W.useInsertionEffect=function(g,P){return Q.current.useInsertionEffect(g,P)},W.useLayoutEffect=function(g,P){return Q.current.useLayoutEffect(g,P)},W.useMemo=function(g,P){return Q.current.useMemo(g,P)},W.useReducer=function(g,P,z){return Q.current.useReducer(g,P,z)},W.useRef=function(g){return Q.current.useRef(g)},W.useState=function(g){return Q.current.useState(g)},W.useSyncExternalStore=function(g,P,z){return Q.current.useSyncExternalStore(g,P,z)},W.useTransition=function(){return Q.current.useTransition()},W.version="18.3.1",W}var jr;function Ra(){return jr||(jr=1,Vt.exports=ba()),Vt.exports}var c=Ra();const ul=Ea(c);var nt={},Ur;function Sa(){if(Ur)return nt;Ur=1,Object.defineProperty(nt,"__esModule",{value:!0}),nt.parse=l,nt.serialize=u;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,n=Object.prototype.toString,o=(()=>{const h=function(){};return h.prototype=Object.create(null),h})();function l(h,v){const w=new o,S=h.length;if(S<2)return w;const b=(v==null?void 0:v.decode)||d;let R=0;do{const L=h.indexOf("=",R);if(L===-1)break;const _=h.indexOf(";",R),D=_===-1?S:_;if(L>D){R=h.lastIndexOf(";",L-1)+1;continue}const C=s(h,R,L),p=i(h,L,C),k=h.slice(C,p);if(w[k]===void 0){let j=s(h,L+1,D),A=i(h,D,j);const q=b(h.slice(j,A));w[k]=q}R=D+1}while(R<S);return w}function s(h,v,w){do{const S=h.charCodeAt(v);if(S!==32&&S!==9)return v}while(++v<w);return w}function i(h,v,w){for(;v>w;){const S=h.charCodeAt(--v);if(S!==32&&S!==9)return v+1}return w}function u(h,v,w){const S=(w==null?void 0:w.encode)||encodeURIComponent;if(!e.test(h))throw new TypeError(`argument name is invalid: ${h}`);const b=S(v);if(!t.test(b))throw new TypeError(`argument val is invalid: ${v}`);let R=h+"="+b;if(!w)return R;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);R+="; Max-Age="+w.maxAge}if(w.domain){if(!r.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);R+="; Domain="+w.domain}if(w.path){if(!a.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);R+="; Path="+w.path}if(w.expires){if(!m(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);R+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(R+="; HttpOnly"),w.secure&&(R+="; Secure"),w.partitioned&&(R+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":R+="; Priority=Low";break;case"medium":R+="; Priority=Medium";break;case"high":R+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":R+="; SameSite=Strict";break;case"lax":R+="; SameSite=Lax";break;case"none":R+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return R}function d(h){if(h.indexOf("%")===-1)return h;try{return decodeURIComponent(h)}catch{return h}}function m(h){return n.call(h)==="[object Date]"}return nt}Sa();/**
 * react-router v7.6.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var hn=e=>{throw TypeError(e)},xa=(e,t,r)=>t.has(e)||hn("Cannot "+r),Wt=(e,t,r)=>(xa(e,t,"read from private field"),r?r.call(e):t.get(e)),La=(e,t,r)=>t.has(e)?hn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Hr="popstate";function cl(e={}){function t(a,n){let{pathname:o,search:l,hash:s}=a.location;return ut("",{pathname:o,search:l,hash:s},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:Fe(n)}return Pa(t,r,null,e)}function H(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ie(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ca(){return Math.random().toString(36).substring(2,10)}function zr(e,t){return{usr:e.state,key:e.key,idx:t}}function ut(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?$e(t):t,state:r,key:t&&t.key||a||Ca()}}function Fe({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function $e(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function Pa(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,l=n.history,s="POP",i=null,u=d();u==null&&(u=0,l.replaceState({...l.state,idx:u},""));function d(){return(l.state||{idx:null}).idx}function m(){s="POP";let b=d(),R=b==null?null:b-u;u=b,i&&i({action:s,location:S.location,delta:R})}function h(b,R){s="PUSH";let L=ut(S.location,b,R);u=d()+1;let _=zr(L,u),D=S.createHref(L);try{l.pushState(_,"",D)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;n.location.assign(D)}o&&i&&i({action:s,location:S.location,delta:1})}function v(b,R){s="REPLACE";let L=ut(S.location,b,R);u=d();let _=zr(L,u),D=S.createHref(L);l.replaceState(_,"",D),o&&i&&i({action:s,location:S.location,delta:0})}function w(b){return mn(b)}let S={get action(){return s},get location(){return e(n,l)},listen(b){if(i)throw new Error("A history only accepts one active listener");return n.addEventListener(Hr,m),i=b,()=>{n.removeEventListener(Hr,m),i=null}},createHref(b){return t(n,b)},createURL:w,encodeLocation(b){let R=w(b);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:h,replace:v,go(b){return l.go(b)}};return S}function mn(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),H(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:Fe(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}var lt,Br=class{constructor(e){if(La(this,lt,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Wt(this,lt).has(e))return Wt(this,lt).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Wt(this,lt).set(e,t)}};lt=new WeakMap;var Ma=new Set(["lazy","caseSensitive","path","id","index","children"]);function Da(e){return Ma.has(e)}var _a=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Ta(e){return _a.has(e)}function ka(e){return e.index===!0}function Dt(e,t,r=[],a={}){return e.map((n,o)=>{let l=[...r,String(o)],s=typeof n.id=="string"?n.id:l.join("-");if(H(n.index!==!0||!n.children,"Cannot specify children on an index route"),H(!a[s],`Found a route id collision on id "${s}".  Route id's must be globally unique within Data Router usages`),ka(n)){let i={...n,...t(n),id:s};return a[s]=i,i}else{let i={...n,...t(n),id:s,children:void 0};return a[s]=i,n.children&&(i.children=Dt(n.children,t,l,a)),i}})}function Pe(e,t,r="/"){return xt(e,t,r,!1)}function xt(e,t,r,a){let n=typeof t=="string"?$e(t):t,o=Ee(n.pathname||"/",r);if(o==null)return null;let l=yn(e);Oa(l);let s=null;for(let i=0;s==null&&i<l.length;++i){let u=Ba(o);s=za(l[i],u,a)}return s}function pn(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}function yn(e,t=[],r=[],a=""){let n=(o,l,s)=>{let i={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(H(i.relativePath.startsWith(a),`Absolute route path "${i.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(a.length));let u=Me([a,i.relativePath]),d=r.concat(i);o.children&&o.children.length>0&&(H(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),yn(o.children,t,d,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:Ua(u,o.index),routesMeta:d})};return e.forEach((o,l)=>{var s;if(o.path===""||!((s=o.path)!=null&&s.includes("?")))n(o,l);else for(let i of vn(o.path))n(o,l,i)}),t}function vn(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let l=vn(a.join("/")),s=[];return s.push(...l.map(i=>i===""?o:[o,i].join("/"))),n&&s.push(...l),s.map(i=>e.startsWith("/")&&i===""?"/":i)}function Oa(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ha(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var Fa=/^:[\w-]+$/,Ia=3,$a=2,Aa=1,Na=10,ja=-2,Vr=e=>e==="*";function Ua(e,t){let r=e.split("/"),a=r.length;return r.some(Vr)&&(a+=ja),t&&(a+=$a),r.filter(n=>!Vr(n)).reduce((n,o)=>n+(Fa.test(o)?Ia:o===""?Aa:Na),a)}function Ha(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function za(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",l=[];for(let s=0;s<a.length;++s){let i=a[s],u=s===a.length-1,d=o==="/"?t:t.slice(o.length)||"/",m=_t({path:i.relativePath,caseSensitive:i.caseSensitive,end:u},d),h=i.route;if(!m&&u&&r&&!a[a.length-1].route.index&&(m=_t({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},d)),!m)return null;Object.assign(n,m.params),l.push({params:n,pathname:Me([o,m.pathname]),pathnameBase:Ya(Me([o,m.pathnameBase])),route:h}),m.pathnameBase!=="/"&&(o=Me([o,m.pathnameBase]))}return l}function _t(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=gn(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],l=o.replace(/(.)\/+$/,"$1"),s=n.slice(1);return{params:a.reduce((u,{paramName:d,isOptional:m},h)=>{if(d==="*"){let w=s[h]||"";l=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const v=s[h];return m&&!v?u[d]=void 0:u[d]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:l,pattern:e}}function gn(e,t=!1,r=!0){ie(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,i)=>(a.push({paramName:s,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function Ba(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ie(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Ee(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Va(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?$e(e):e;return{pathname:r?r.startsWith("/")?r:Wa(r,t):t,search:Ja(a),hash:Ka(n)}}function Wa(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function Yt(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function wn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function cr(e){let t=wn(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function dr(e,t,r,a=!1){let n;typeof e=="string"?n=$e(e):(n={...e},H(!n.pathname||!n.pathname.includes("?"),Yt("?","pathname","search",n)),H(!n.pathname||!n.pathname.includes("#"),Yt("#","pathname","hash",n)),H(!n.search||!n.search.includes("#"),Yt("#","search","hash",n)));let o=e===""||n.pathname==="",l=o?"/":n.pathname,s;if(l==null)s=r;else{let m=t.length-1;if(!a&&l.startsWith("..")){let h=l.split("/");for(;h[0]==="..";)h.shift(),m-=1;n.pathname=h.join("/")}s=m>=0?t[m]:"/"}let i=Va(n,s),u=l&&l!=="/"&&l.endsWith("/"),d=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(u||d)&&(i.pathname+="/"),i}var Me=e=>e.join("/").replace(/\/\/+/g,"/"),Ya=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ja=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ka=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Ga=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function Xa(e,t){return new Ga(e,typeof t=="number"?{status:t}:t)}var qa=(e,t=302)=>{let r=t;typeof r=="number"?r={status:r}:typeof r.status>"u"&&(r.status=302);let a=new Headers(r.headers);return a.set("Location",e),new Response(null,{...r,headers:a})},Ie=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Be(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var En=["POST","PUT","PATCH","DELETE"],Qa=new Set(En),Za=["GET",...En],eo=new Set(Za),to=new Set([301,302,303,307,308]),ro=new Set([307,308]),Jt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},bn={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},at={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},fr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,no=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Rn="remix-router-transitions",Sn=Symbol("ResetLoaderData");function dl(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";H(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a=e.hydrationRouteProperties||[],n=e.mapRouteProperties||no,o={},l=Dt(e.routes,n,void 0,o),s,i=e.basename||"/",u=e.dataStrategy||so,d={unstable_middleware:!1,...e.future},m=null,h=new Set,v=null,w=null,S=null,b=e.hydrationData!=null,R=Pe(l,e.history.location,i),L=!1,_=null,D;if(R==null&&!e.patchRoutesOnNavigation){let f=Se(404,{pathname:e.history.location.pathname}),{matches:y,route:E}=rn(l);D=!0,R=y,_={[E.id]:f}}else if(R&&!e.hydrationData&&pt(R,l,e.history.location.pathname).active&&(R=null),R)if(R.some(f=>f.route.lazy))D=!1;else if(!R.some(f=>f.route.loader))D=!0;else{let f=e.hydrationData?e.hydrationData.loaderData:null,y=e.hydrationData?e.hydrationData.errors:null;if(y){let E=R.findIndex(x=>y[x.route.id]!==void 0);D=R.slice(0,E+1).every(x=>!er(x.route,f,y))}else D=R.every(E=>!er(E.route,f,y))}else{D=!1,R=[];let f=pt(null,l,e.history.location.pathname);f.active&&f.matches&&(L=!0,R=f.matches)}let C,p={historyAction:e.history.action,location:e.history.location,matches:R,initialized:D,navigation:Jt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||_,fetchers:new Map,blockers:new Map},k="POP",j=!1,A,q=!1,ae=new Map,fe=null,V=!1,G=!1,le=new Set,Y=new Map,Q=0,he=-1,pe=new Map,de=new Set,g=new Map,P=new Map,z=new Set,J=new Map,X,Z=null;function se(){if(m=e.history.listen(({action:f,location:y,delta:E})=>{if(X){X(),X=void 0;return}ie(J.size===0||E!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let x=Fr({currentLocation:p.location,nextLocation:y,historyAction:f});if(x&&E!=null){let M=new Promise(O=>{X=O});e.history.go(E*-1),mt(x,{state:"blocked",location:y,proceed(){mt(x,{state:"proceeding",proceed:void 0,reset:void 0,location:y}),M.then(()=>e.history.go(E))},reset(){let O=new Map(p.blockers);O.set(x,at),te({blockers:O})}});return}return Ae(f,y)}),r){Eo(t,ae);let f=()=>bo(t,ae);t.addEventListener("pagehide",f),fe=()=>t.removeEventListener("pagehide",f)}return p.initialized||Ae("POP",p.location,{initialHydration:!0}),C}function oe(){m&&m(),fe&&fe(),h.clear(),A&&A.abort(),p.fetchers.forEach((f,y)=>Ut(y)),p.blockers.forEach((f,y)=>Or(y))}function ne(f){return h.add(f),()=>h.delete(f)}function te(f,y={}){p={...p,...f};let E=[],x=[];p.fetchers.forEach((M,O)=>{M.state==="idle"&&(z.has(O)?E.push(O):x.push(O))}),z.forEach(M=>{!p.fetchers.has(M)&&!Y.has(M)&&E.push(M)}),[...h].forEach(M=>M(p,{deletedFetchers:E,viewTransitionOpts:y.viewTransitionOpts,flushSync:y.flushSync===!0})),E.forEach(M=>Ut(M)),x.forEach(M=>p.fetchers.delete(M))}function We(f,y,{flushSync:E}={}){var $,U;let x=p.actionData!=null&&p.navigation.formMethod!=null&&we(p.navigation.formMethod)&&p.navigation.state==="loading"&&(($=f.state)==null?void 0:$._isRedirect)!==!0,M;y.actionData?Object.keys(y.actionData).length>0?M=y.actionData:M=null:x?M=p.actionData:M=null;let O=y.loaderData?en(p.loaderData,y.loaderData,y.matches||[],y.errors):p.loaderData,N=p.blockers;N.size>0&&(N=new Map(N),N.forEach((I,B)=>N.set(B,at)));let T=j===!0||p.navigation.formMethod!=null&&we(p.navigation.formMethod)&&((U=f.state)==null?void 0:U._isRedirect)!==!0;s&&(l=s,s=void 0),V||k==="POP"||(k==="PUSH"?e.history.push(f,f.state):k==="REPLACE"&&e.history.replace(f,f.state));let F;if(k==="POP"){let I=ae.get(p.location.pathname);I&&I.has(f.pathname)?F={currentLocation:p.location,nextLocation:f}:ae.has(f.pathname)&&(F={currentLocation:f,nextLocation:p.location})}else if(q){let I=ae.get(p.location.pathname);I?I.add(f.pathname):(I=new Set([f.pathname]),ae.set(p.location.pathname,I)),F={currentLocation:p.location,nextLocation:f}}te({...y,actionData:M,loaderData:O,historyAction:k,location:f,initialized:!0,navigation:Jt,revalidation:"idle",restoreScrollPosition:$r(f,y.matches||p.matches),preventScrollReset:T,blockers:N},{viewTransitionOpts:F,flushSync:E===!0}),k="POP",j=!1,q=!1,V=!1,G=!1,Z==null||Z.resolve(),Z=null}async function Cr(f,y){if(typeof f=="number"){e.history.go(f);return}let E=Zt(p.location,p.matches,i,f,y==null?void 0:y.fromRouteId,y==null?void 0:y.relative),{path:x,submission:M,error:O}=Wr(!1,E,y),N=p.location,T=ut(p.location,x,y&&y.state);T={...T,...e.history.encodeLocation(T)};let F=y&&y.replace!=null?y.replace:void 0,$="PUSH";F===!0?$="REPLACE":F===!1||M!=null&&we(M.formMethod)&&M.formAction===p.location.pathname+p.location.search&&($="REPLACE");let U=y&&"preventScrollReset"in y?y.preventScrollReset===!0:void 0,I=(y&&y.flushSync)===!0,B=Fr({currentLocation:N,nextLocation:T,historyAction:$});if(B){mt(B,{state:"blocked",location:T,proceed(){mt(B,{state:"proceeding",proceed:void 0,reset:void 0,location:T}),Cr(f,y)},reset(){let re=new Map(p.blockers);re.set(B,at),te({blockers:re})}});return}await Ae($,T,{submission:M,pendingError:O,preventScrollReset:U,replace:y&&y.replace,enableViewTransition:y&&y.viewTransition,flushSync:I})}function ia(){Z||(Z=Ro()),jt(),te({revalidation:"loading"});let f=Z.promise;return p.navigation.state==="submitting"?f:p.navigation.state==="idle"?(Ae(p.historyAction,p.location,{startUninterruptedRevalidation:!0}),f):(Ae(k||p.historyAction,p.navigation.location,{overrideNavigation:p.navigation,enableViewTransition:q===!0}),f)}async function Ae(f,y,E){A&&A.abort(),A=null,k=f,V=(E&&E.startUninterruptedRevalidation)===!0,ya(p.location,p.matches),j=(E&&E.preventScrollReset)===!0,q=(E&&E.enableViewTransition)===!0;let x=s||l,M=E&&E.overrideNavigation,O=E!=null&&E.initialHydration&&p.matches&&p.matches.length>0&&!L?p.matches:Pe(x,y,i),N=(E&&E.flushSync)===!0;if(O&&p.initialized&&!G&&po(p.location,y)&&!(E&&E.submission&&we(E.submission.formMethod))){We(y,{matches:O},{flushSync:N});return}let T=pt(O,x,y.pathname);if(T.active&&T.matches&&(O=T.matches),!O){let{error:me,notFoundMatches:ve,route:K}=Ht(y.pathname);We(y,{matches:ve,loaderData:{},errors:{[K.id]:me}},{flushSync:N});return}A=new AbortController;let F=Je(e.history,y,A.signal,E&&E.submission),$=new Br(e.unstable_getContext?await e.unstable_getContext():void 0),U;if(E&&E.pendingError)U=[He(O).route.id,{type:"error",error:E.pendingError}];else if(E&&E.submission&&we(E.submission.formMethod)){let me=await la(F,y,E.submission,O,$,T.active,E&&E.initialHydration===!0,{replace:E.replace,flushSync:N});if(me.shortCircuited)return;if(me.pendingActionResult){let[ve,K]=me.pendingActionResult;if(ge(K)&&Be(K.error)&&K.error.status===404){A=null,We(y,{matches:me.matches,loaderData:{},errors:{[ve]:K.error}});return}}O=me.matches||O,U=me.pendingActionResult,M=Kt(y,E.submission),N=!1,T.active=!1,F=Je(e.history,F.url,F.signal)}let{shortCircuited:I,matches:B,loaderData:re,errors:ce}=await sa(F,y,O,$,T.active,M,E&&E.submission,E&&E.fetcherSubmission,E&&E.replace,E&&E.initialHydration===!0,N,U);I||(A=null,We(y,{matches:B||O,...tn(U),loaderData:re,errors:ce}))}async function la(f,y,E,x,M,O,N,T={}){jt();let F=go(y,E);if(te({navigation:F},{flushSync:T.flushSync===!0}),O){let I=await yt(x,y.pathname,f.signal);if(I.type==="aborted")return{shortCircuited:!0};if(I.type==="error"){let B=He(I.partialMatches).route.id;return{matches:I.partialMatches,pendingActionResult:[B,{type:"error",error:I.error}]}}else if(I.matches)x=I.matches;else{let{notFoundMatches:B,error:re,route:ce}=Ht(y.pathname);return{matches:B,pendingActionResult:[ce.id,{type:"error",error:re}]}}}let $,U=st(x,y);if(!U.route.action&&!U.route.lazy)$={type:"error",error:Se(405,{method:f.method,pathname:y.pathname,routeId:U.route.id})};else{let I=Ke(n,o,f,x,U,N?[]:a,M),B=await Qe(f,I,M,null);if($=B[U.route.id],!$){for(let re of x)if(B[re.route.id]){$=B[re.route.id];break}}if(f.signal.aborted)return{shortCircuited:!0}}if(ze($)){let I;return T&&T.replace!=null?I=T.replace:I=qr($.response.headers.get("Location"),new URL(f.url),i)===p.location.pathname+p.location.search,await Ne(f,$,!0,{submission:E,replace:I}),{shortCircuited:!0}}if(ge($)){let I=He(x,U.route.id);return(T&&T.replace)!==!0&&(k="PUSH"),{matches:x,pendingActionResult:[I.route.id,$,U.route.id]}}return{matches:x,pendingActionResult:[U.route.id,$]}}async function sa(f,y,E,x,M,O,N,T,F,$,U,I){let B=O||Kt(y,N),re=N||T||nn(B),ce=!V&&!$;if(M){if(ce){let Re=Pr(I);te({navigation:B,...Re!==void 0?{actionData:Re}:{}},{flushSync:U})}let ee=await yt(E,y.pathname,f.signal);if(ee.type==="aborted")return{shortCircuited:!0};if(ee.type==="error"){let Re=He(ee.partialMatches).route.id;return{matches:ee.partialMatches,loaderData:{},errors:{[Re]:ee.error}}}else if(ee.matches)E=ee.matches;else{let{error:Re,notFoundMatches:ke,route:gt}=Ht(y.pathname);return{matches:ke,loaderData:{},errors:{[gt.id]:Re}}}}let me=s||l,{dsMatches:ve,revalidatingFetchers:K}=Yr(f,x,n,o,e.history,p,E,re,y,$?[]:a,$===!0,G,le,z,g,de,me,i,I);if(he=++Q,!e.dataStrategy&&!ve.some(ee=>ee.shouldLoad)&&K.length===0){let ee=Tr();return We(y,{matches:E,loaderData:{},errors:I&&ge(I[1])?{[I[0]]:I[1].error}:null,...tn(I),...ee?{fetchers:new Map(p.fetchers)}:{}},{flushSync:U}),{shortCircuited:!0}}if(ce){let ee={};if(!M){ee.navigation=B;let Re=Pr(I);Re!==void 0&&(ee.actionData=Re)}K.length>0&&(ee.fetchers=ua(K)),te(ee,{flushSync:U})}K.forEach(ee=>{Te(ee.key),ee.controller&&Y.set(ee.key,ee.controller)});let Ze=()=>K.forEach(ee=>Te(ee.key));A&&A.signal.addEventListener("abort",Ze);let{loaderResults:je,fetcherResults:et}=await Mr(ve,K,f,x);if(f.signal.aborted)return{shortCircuited:!0};A&&A.signal.removeEventListener("abort",Ze),K.forEach(ee=>Y.delete(ee.key));let be=wt(je);if(be)return await Ne(f,be.result,!0,{replace:F}),{shortCircuited:!0};if(be=wt(et),be)return de.add(be.key),await Ne(f,be.result,!0,{replace:F}),{shortCircuited:!0};let{loaderData:tt,errors:rt}=Zr(p,E,je,I,K,et);$&&p.errors&&(rt={...p.errors,...rt});let zt=Tr(),Ue=kr(he),vt=zt||Ue||K.length>0;return{matches:E,loaderData:tt,errors:rt,...vt?{fetchers:new Map(p.fetchers)}:{}}}function Pr(f){if(f&&!ge(f[1]))return{[f[0]]:f[1].data};if(p.actionData)return Object.keys(p.actionData).length===0?null:p.actionData}function ua(f){return f.forEach(y=>{let E=p.fetchers.get(y.key),x=ot(void 0,E?E.data:void 0);p.fetchers.set(y.key,x)}),new Map(p.fetchers)}async function ca(f,y,E,x){Te(f);let M=(x&&x.flushSync)===!0,O=s||l,N=Zt(p.location,p.matches,i,E,y,x==null?void 0:x.relative),T=Pe(O,N,i),F=pt(T,O,N);if(F.active&&F.matches&&(T=F.matches),!T){De(f,y,Se(404,{pathname:N}),{flushSync:M});return}let{path:$,submission:U,error:I}=Wr(!0,N,x);if(I){De(f,y,I,{flushSync:M});return}let B=st(T,$),re=new Br(e.unstable_getContext?await e.unstable_getContext():void 0),ce=(x&&x.preventScrollReset)===!0;if(U&&we(U.formMethod)){await da(f,y,$,B,T,re,F.active,M,ce,U);return}g.set(f,{routeId:y,path:$}),await fa(f,y,$,B,T,re,F.active,M,ce,U)}async function da(f,y,E,x,M,O,N,T,F,$){jt(),g.delete(f);function U(ue){if(!ue.route.action&&!ue.route.lazy){let Ye=Se(405,{method:$.formMethod,pathname:E,routeId:y});return De(f,y,Ye,{flushSync:T}),!0}return!1}if(!N&&U(x))return;let I=p.fetchers.get(f);_e(f,wo($,I),{flushSync:T});let B=new AbortController,re=Je(e.history,E,B.signal,$);if(N){let ue=await yt(M,E,re.signal,f);if(ue.type==="aborted")return;if(ue.type==="error"){De(f,y,ue.error,{flushSync:T});return}else if(ue.matches){if(M=ue.matches,x=st(M,E),U(x))return}else{De(f,y,Se(404,{pathname:E}),{flushSync:T});return}}Y.set(f,B);let ce=Q,me=Ke(n,o,re,M,x,a,O),K=(await Qe(re,me,O,f))[x.route.id];if(re.signal.aborted){Y.get(f)===B&&Y.delete(f);return}if(z.has(f)){if(ze(K)||ge(K)){_e(f,Oe(void 0));return}}else{if(ze(K))if(Y.delete(f),he>ce){_e(f,Oe(void 0));return}else return de.add(f),_e(f,ot($)),Ne(re,K,!1,{fetcherSubmission:$,preventScrollReset:F});if(ge(K)){De(f,y,K.error);return}}let Ze=p.navigation.location||p.location,je=Je(e.history,Ze,B.signal),et=s||l,be=p.navigation.state!=="idle"?Pe(et,p.navigation.location,i):p.matches;H(be,"Didn't find any matches after fetcher action");let tt=++Q;pe.set(f,tt);let rt=ot($,K.data);p.fetchers.set(f,rt);let{dsMatches:zt,revalidatingFetchers:Ue}=Yr(je,O,n,o,e.history,p,be,$,Ze,a,!1,G,le,z,g,de,et,i,[x.route.id,K]);Ue.filter(ue=>ue.key!==f).forEach(ue=>{let Ye=ue.key,Ar=p.fetchers.get(Ye),wa=ot(void 0,Ar?Ar.data:void 0);p.fetchers.set(Ye,wa),Te(Ye),ue.controller&&Y.set(Ye,ue.controller)}),te({fetchers:new Map(p.fetchers)});let vt=()=>Ue.forEach(ue=>Te(ue.key));B.signal.addEventListener("abort",vt);let{loaderResults:ee,fetcherResults:Re}=await Mr(zt,Ue,je,O);if(B.signal.aborted)return;if(B.signal.removeEventListener("abort",vt),pe.delete(f),Y.delete(f),Ue.forEach(ue=>Y.delete(ue.key)),p.fetchers.has(f)){let ue=Oe(K.data);p.fetchers.set(f,ue)}let ke=wt(ee);if(ke)return Ne(je,ke.result,!1,{preventScrollReset:F});if(ke=wt(Re),ke)return de.add(ke.key),Ne(je,ke.result,!1,{preventScrollReset:F});let{loaderData:gt,errors:Bt}=Zr(p,be,ee,void 0,Ue,Re);kr(tt),p.navigation.state==="loading"&&tt>he?(H(k,"Expected pending action"),A&&A.abort(),We(p.navigation.location,{matches:be,loaderData:gt,errors:Bt,fetchers:new Map(p.fetchers)})):(te({errors:Bt,loaderData:en(p.loaderData,gt,be,Bt),fetchers:new Map(p.fetchers)}),G=!1)}async function fa(f,y,E,x,M,O,N,T,F,$){let U=p.fetchers.get(f);_e(f,ot($,U?U.data:void 0),{flushSync:T});let I=new AbortController,B=Je(e.history,E,I.signal);if(N){let K=await yt(M,E,B.signal,f);if(K.type==="aborted")return;if(K.type==="error"){De(f,y,K.error,{flushSync:T});return}else if(K.matches)M=K.matches,x=st(M,E);else{De(f,y,Se(404,{pathname:E}),{flushSync:T});return}}Y.set(f,I);let re=Q,ce=Ke(n,o,B,M,x,a,O),ve=(await Qe(B,ce,O,f))[x.route.id];if(Y.get(f)===I&&Y.delete(f),!B.signal.aborted){if(z.has(f)){_e(f,Oe(void 0));return}if(ze(ve))if(he>re){_e(f,Oe(void 0));return}else{de.add(f),await Ne(B,ve,!1,{preventScrollReset:F});return}if(ge(ve)){De(f,y,ve.error);return}_e(f,Oe(ve.data))}}async function Ne(f,y,E,{submission:x,fetcherSubmission:M,preventScrollReset:O,replace:N}={}){y.response.headers.has("X-Remix-Revalidate")&&(G=!0);let T=y.response.headers.get("Location");H(T,"Expected a Location header on the redirect Response"),T=qr(T,new URL(f.url),i);let F=ut(p.location,T,{_isRedirect:!0});if(r){let ce=!1;if(y.response.headers.has("X-Remix-Reload-Document"))ce=!0;else if(fr.test(T)){const me=mn(T,!0);ce=me.origin!==t.location.origin||Ee(me.pathname,i)==null}if(ce){N?t.location.replace(T):t.location.assign(T);return}}A=null;let $=N===!0||y.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:U,formAction:I,formEncType:B}=p.navigation;!x&&!M&&U&&I&&B&&(x=nn(p.navigation));let re=x||M;if(ro.has(y.response.status)&&re&&we(re.formMethod))await Ae($,F,{submission:{...re,formAction:T},preventScrollReset:O||j,enableViewTransition:E?q:void 0});else{let ce=Kt(F,x);await Ae($,F,{overrideNavigation:ce,fetcherSubmission:M,preventScrollReset:O||j,enableViewTransition:E?q:void 0})}}async function Qe(f,y,E,x){let M,O={};try{M=await uo(u,f,y,x,E,!1)}catch(N){return y.filter(T=>T.shouldLoad).forEach(T=>{O[T.route.id]={type:"error",error:N}}),O}if(f.signal.aborted)return O;for(let[N,T]of Object.entries(M))if(yo(T)){let F=T.result;O[N]={type:"redirect",response:ho(F,f,N,y,i)}}else O[N]=await fo(T);return O}async function Mr(f,y,E,x){let M=Qe(E,f,x,null),O=Promise.all(y.map(async F=>{if(F.matches&&F.match&&F.request&&F.controller){let U=(await Qe(F.request,F.matches,x,F.key))[F.match.route.id];return{[F.key]:U}}else return Promise.resolve({[F.key]:{type:"error",error:Se(404,{pathname:F.path})}})})),N=await M,T=(await O).reduce((F,$)=>Object.assign(F,$),{});return{loaderResults:N,fetcherResults:T}}function jt(){G=!0,g.forEach((f,y)=>{Y.has(y)&&le.add(y),Te(y)})}function _e(f,y,E={}){p.fetchers.set(f,y),te({fetchers:new Map(p.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function De(f,y,E,x={}){let M=He(p.matches,y);Ut(f),te({errors:{[M.route.id]:E},fetchers:new Map(p.fetchers)},{flushSync:(x&&x.flushSync)===!0})}function Dr(f){return P.set(f,(P.get(f)||0)+1),z.has(f)&&z.delete(f),p.fetchers.get(f)||bn}function Ut(f){let y=p.fetchers.get(f);Y.has(f)&&!(y&&y.state==="loading"&&pe.has(f))&&Te(f),g.delete(f),pe.delete(f),de.delete(f),z.delete(f),le.delete(f),p.fetchers.delete(f)}function ha(f){let y=(P.get(f)||0)-1;y<=0?(P.delete(f),z.add(f)):P.set(f,y),te({fetchers:new Map(p.fetchers)})}function Te(f){let y=Y.get(f);y&&(y.abort(),Y.delete(f))}function _r(f){for(let y of f){let E=Dr(y),x=Oe(E.data);p.fetchers.set(y,x)}}function Tr(){let f=[],y=!1;for(let E of de){let x=p.fetchers.get(E);H(x,`Expected fetcher: ${E}`),x.state==="loading"&&(de.delete(E),f.push(E),y=!0)}return _r(f),y}function kr(f){let y=[];for(let[E,x]of pe)if(x<f){let M=p.fetchers.get(E);H(M,`Expected fetcher: ${E}`),M.state==="loading"&&(Te(E),pe.delete(E),y.push(E))}return _r(y),y.length>0}function ma(f,y){let E=p.blockers.get(f)||at;return J.get(f)!==y&&J.set(f,y),E}function Or(f){p.blockers.delete(f),J.delete(f)}function mt(f,y){let E=p.blockers.get(f)||at;H(E.state==="unblocked"&&y.state==="blocked"||E.state==="blocked"&&y.state==="blocked"||E.state==="blocked"&&y.state==="proceeding"||E.state==="blocked"&&y.state==="unblocked"||E.state==="proceeding"&&y.state==="unblocked",`Invalid blocker state transition: ${E.state} -> ${y.state}`);let x=new Map(p.blockers);x.set(f,y),te({blockers:x})}function Fr({currentLocation:f,nextLocation:y,historyAction:E}){if(J.size===0)return;J.size>1&&ie(!1,"A router only supports one blocker at a time");let x=Array.from(J.entries()),[M,O]=x[x.length-1],N=p.blockers.get(M);if(!(N&&N.state==="proceeding")&&O({currentLocation:f,nextLocation:y,historyAction:E}))return M}function Ht(f){let y=Se(404,{pathname:f}),E=s||l,{matches:x,route:M}=rn(E);return{notFoundMatches:x,route:M,error:y}}function pa(f,y,E){if(v=f,S=y,w=E||null,!b&&p.navigation===Jt){b=!0;let x=$r(p.location,p.matches);x!=null&&te({restoreScrollPosition:x})}return()=>{v=null,S=null,w=null}}function Ir(f,y){return w&&w(f,y.map(x=>pn(x,p.loaderData)))||f.key}function ya(f,y){if(v&&S){let E=Ir(f,y);v[E]=S()}}function $r(f,y){if(v){let E=Ir(f,y),x=v[E];if(typeof x=="number")return x}return null}function pt(f,y,E){if(e.patchRoutesOnNavigation)if(f){if(Object.keys(f[0].params).length>0)return{active:!0,matches:xt(y,E,i,!0)}}else return{active:!0,matches:xt(y,E,i,!0)||[]};return{active:!1,matches:null}}async function yt(f,y,E,x){if(!e.patchRoutesOnNavigation)return{type:"success",matches:f};let M=f;for(;;){let O=s==null,N=s||l,T=o;try{await e.patchRoutesOnNavigation({signal:E,path:y,matches:M,fetcherKey:x,patch:(U,I)=>{E.aborted||Jr(U,I,N,T,n)}})}catch(U){return{type:"error",error:U,partialMatches:M}}finally{O&&!E.aborted&&(l=[...l])}if(E.aborted)return{type:"aborted"};let F=Pe(N,y,i);if(F)return{type:"success",matches:F};let $=xt(N,y,i,!0);if(!$||M.length===$.length&&M.every((U,I)=>U.route.id===$[I].route.id))return{type:"success",matches:null};M=$}}function va(f){o={},s=Dt(f,n,void 0,o)}function ga(f,y){let E=s==null;Jr(f,y,s||l,o,n),E&&(l=[...l],te({}))}return C={get basename(){return i},get future(){return d},get state(){return p},get routes(){return l},get window(){return t},initialize:se,subscribe:ne,enableScrollRestoration:pa,navigate:Cr,fetch:ca,revalidate:ia,createHref:f=>e.history.createHref(f),encodeLocation:f=>e.history.encodeLocation(f),getFetcher:Dr,deleteFetcher:ha,dispose:oe,getBlocker:ma,deleteBlocker:Or,patchRoutes:ga,_internalFetchControllers:Y,_internalSetRoutes:va},C}function ao(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Zt(e,t,r,a,n,o){let l,s;if(n){l=[];for(let u of t)if(l.push(u),u.route.id===n){s=u;break}}else l=t,s=t[t.length-1];let i=dr(a||".",cr(l),Ee(e.pathname,r)||e.pathname,o==="path");if(a==null&&(i.search=e.search,i.hash=e.hash),(a==null||a===""||a===".")&&s){let u=mr(i.search);if(s.route.index&&!u)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&u){let d=new URLSearchParams(i.search),m=d.getAll("index");d.delete("index"),m.filter(v=>v).forEach(v=>d.append("index",v));let h=d.toString();i.search=h?`?${h}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:Me([r,i.pathname])),Fe(i)}function Wr(e,t,r){if(!r||!ao(r))return{path:t};if(r.formMethod&&!vo(r.formMethod))return{path:t,error:Se(405,{method:r.formMethod})};let a=()=>({path:t,error:Se(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=Dn(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!we(o))return a();let m=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[v,w])=>`${h}${v}=${w}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:m}}}else if(r.formEncType==="application/json"){if(!we(o))return a();try{let m=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:m,text:void 0}}}catch{return a()}}}H(typeof FormData=="function","FormData is not available in this environment");let s,i;if(r.formData)s=rr(r.formData),i=r.formData;else if(r.body instanceof FormData)s=rr(r.body),i=r.body;else if(r.body instanceof URLSearchParams)s=r.body,i=Qr(s);else if(r.body==null)s=new URLSearchParams,i=new FormData;else try{s=new URLSearchParams(r.body),i=Qr(s)}catch{return a()}let u={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(we(u.formMethod))return{path:t,submission:u};let d=$e(t);return e&&d.search&&mr(d.search)&&s.append("index",""),d.search=`?${s}`,{path:Fe(d),submission:u}}function Yr(e,t,r,a,n,o,l,s,i,u,d,m,h,v,w,S,b,R,L){var fe;let _=L?ge(L[1])?L[1].error:L[1].data:void 0,D=n.createURL(o.location),C=n.createURL(i),p;if(d&&o.errors){let V=Object.keys(o.errors)[0];p=l.findIndex(G=>G.route.id===V)}else if(L&&ge(L[1])){let V=L[0];p=l.findIndex(G=>G.route.id===V)-1}let k=L?L[1].statusCode:void 0,j=k&&k>=400,A={currentUrl:D,currentParams:((fe=o.matches[0])==null?void 0:fe.params)||{},nextUrl:C,nextParams:l[0].params,...s,actionResult:_,actionStatus:k},q=l.map((V,G)=>{let{route:le}=V,Y=null;if(p!=null&&G>p?Y=!1:le.lazy?Y=!0:le.loader==null?Y=!1:d?Y=er(le,o.loaderData,o.errors):oo(o.loaderData,o.matches[G],V)&&(Y=!0),Y!==null)return tr(r,a,e,V,u,t,Y);let Q=j?!1:m||D.pathname+D.search===C.pathname+C.search||D.search!==C.search||io(o.matches[G],V),he={...A,defaultShouldRevalidate:Q},pe=Tt(V,he);return tr(r,a,e,V,u,t,pe,he)}),ae=[];return w.forEach((V,G)=>{if(d||!l.some(g=>g.route.id===V.routeId)||v.has(G))return;let le=Pe(b,V.path,R);if(!le){ae.push({key:G,routeId:V.routeId,path:V.path,matches:null,match:null,request:null,controller:null});return}if(S.has(G))return;let Y=o.fetchers.get(G),Q=st(le,V.path),he=new AbortController,pe=Je(n,V.path,he.signal),de=null;if(h.has(G))h.delete(G),de=Ke(r,a,pe,le,Q,u,t);else if(Y&&Y.state!=="idle"&&Y.data===void 0)m&&(de=Ke(r,a,pe,le,Q,u,t));else{let g={...A,defaultShouldRevalidate:j?!1:m};Tt(Q,g)&&(de=Ke(r,a,pe,le,Q,u,t,g))}de&&ae.push({key:G,routeId:V.routeId,path:V.path,matches:de,match:Q,request:pe,controller:he})}),{dsMatches:q,revalidatingFetchers:ae}}function er(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=t!=null&&e.id in t,n=r!=null&&r[e.id]!==void 0;return!a&&n?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!a&&!n}function oo(e,t,r){let a=!t||r.route.id!==t.route.id,n=!e.hasOwnProperty(r.route.id);return a||n}function io(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Tt(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Jr(e,t,r,a,n){let o;if(e){let i=a[e];H(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let l=t.filter(i=>!o.some(u=>xn(i,u))),s=Dt(l,n,[e||"_","patch",String((o==null?void 0:o.length)||"0")],a);o.push(...s)}function xn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,a)=>{var n;return(n=t.children)==null?void 0:n.some(o=>xn(r,o))}):!1}var Kr=new WeakMap,Ln=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(H(n,"No route found in manifest"),!n.lazy||typeof n.lazy!="object")return;let o=n.lazy[e];if(!o)return;let l=Kr.get(n);l||(l={},Kr.set(n,l));let s=l[e];if(s)return s;let i=(async()=>{let u=Da(e),m=n[e]!==void 0&&e!=="hasErrorBoundary";if(u)ie(!u,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(m)ie(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await o();h!=null&&(Object.assign(n,{[e]:h}),Object.assign(n,a(n)))}typeof n.lazy=="object"&&(n.lazy[e]=void 0,Object.values(n.lazy).every(h=>h===void 0)&&(n.lazy=void 0))})();return l[e]=i,i},Gr=new WeakMap;function lo(e,t,r,a,n){let o=r[e.id];if(H(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=Gr.get(o);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let m=(async()=>{H(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),v={};for(let w in h){let S=h[w];if(S===void 0)continue;let b=Ta(w),L=o[w]!==void 0&&w!=="hasErrorBoundary";b?ie(!b,"Route property "+w+" is not a supported property to be returned from a lazy route function. This property will be ignored."):L?ie(!L,`Route "${o.id}" has a static property "${w}" defined but its lazy function is also returning a value for this property. The lazy route property "${w}" will be ignored.`):v[w]=S}Object.assign(o,v),Object.assign(o,{...a(o),lazy:void 0})})();return Gr.set(o,m),m.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:m}}let l=Object.keys(e.lazy),s=[],i;for(let d of l){if(n&&n.includes(d))continue;let m=Ln({key:d,route:e,manifest:r,mapRouteProperties:a});m&&(s.push(m),d===t&&(i=m))}let u=s.length>0?Promise.all(s).then(()=>{}):void 0;return u==null||u.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:i}}async function Xr(e){let t=e.matches.filter(n=>n.shouldLoad),r={};return(await Promise.all(t.map(n=>n.resolve()))).forEach((n,o)=>{r[t[o].route.id]=n}),r}async function so(e){return e.matches.some(t=>t.route.unstable_middleware)?Cn(e,!1,()=>Xr(e),(t,r)=>({[r]:{type:"error",result:t}})):Xr(e)}async function Cn(e,t,r,a){let{matches:n,request:o,params:l,context:s}=e,i={handlerResult:void 0};try{let u=n.flatMap(m=>m.route.unstable_middleware?m.route.unstable_middleware.map(h=>[m.route.id,h]):[]),d=await Pn({request:o,params:l,context:s},u,t,i,r);return t?d:i.handlerResult}catch(u){if(!i.middlewareError)throw u;let d=await a(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,d):d}}async function Pn(e,t,r,a,n,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let s=t[o];if(!s)return a.handlerResult=await n(),a.handlerResult;let[i,u]=s,d=!1,m,h=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0,await Pn(e,t,r,a,n,o+1)};try{let v=await u({request:e.request,params:e.params,context:e.context},h);return d?v===void 0?m:v:h()}catch(v){throw a.middlewareError?a.middlewareError.error!==v&&(a.middlewareError={routeId:i,error:v}):a.middlewareError={routeId:i,error:v},v}}function Mn(e,t,r,a,n){let o=Ln({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),l=lo(a.route,we(r.method)?"action":"loader",t,e,n);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function tr(e,t,r,a,n,o,l,s=null){let i=!1,u=Mn(e,t,r,a,n);return{...a,_lazyPromises:u,shouldLoad:l,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler(d){return i=!0,s?typeof d=="boolean"?Tt(a,{...s,defaultShouldRevalidate:d}):Tt(a,s):l},resolve(d){return i||l||d&&r.method==="GET"&&(a.route.lazy||a.route.loader)?co({request:r,match:a,lazyHandlerPromise:u==null?void 0:u.handler,lazyRoutePromise:u==null?void 0:u.route,handlerOverride:d,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Ke(e,t,r,a,n,o,l,s=null){return a.map(i=>i.route.id!==n.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:()=>!1,_lazyPromises:Mn(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:tr(e,t,r,i,o,l,!0,s))}async function uo(e,t,r,a,n,o){r.some(u=>{var d;return(d=u._lazyPromises)==null?void 0:d.middleware})&&await Promise.all(r.map(u=>{var d;return(d=u._lazyPromises)==null?void 0:d.middleware}));let l={request:t,params:r[0].params,context:n,matches:r},i=await e({...l,fetcherKey:a,unstable_runClientMiddleware:u=>{let d=l;return Cn(d,!1,()=>u({...d,fetcherKey:a,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(m,h)=>({[h]:{type:"error",result:m}}))}});try{await Promise.all(r.flatMap(u=>{var d,m;return[(d=u._lazyPromises)==null?void 0:d.handler,(m=u._lazyPromises)==null?void 0:m.route]}))}catch{}return i}async function co({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:o}){let l,s,i=we(e.method),u=i?"action":"loader",d=m=>{let h,v=new Promise((b,R)=>h=R);s=()=>h(),e.signal.addEventListener("abort",s);let w=b=>typeof m!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):m({request:e,params:t.params,context:o},...b!==void 0?[b]:[]),S=(async()=>{try{return{type:"data",result:await(n?n(R=>w(R)):w())}}catch(b){return{type:"error",result:b}}})();return Promise.race([S,v])};try{let m=i?t.route.action:t.route.loader;if(r||a)if(m){let h,[v]=await Promise.all([d(m).catch(w=>{h=w}),r,a]);if(h!==void 0)throw h;l=v}else{await r;let h=i?t.route.action:t.route.loader;if(h)[l]=await Promise.all([d(h),a]);else if(u==="action"){let v=new URL(e.url),w=v.pathname+v.search;throw Se(405,{method:e.method,pathname:w,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(m)l=await d(m);else{let h=new URL(e.url),v=h.pathname+h.search;throw Se(404,{pathname:v})}}catch(m){return{type:"error",result:m}}finally{s&&e.signal.removeEventListener("abort",s)}return l}async function fo(e){var a,n,o,l,s,i;let{result:t,type:r}=e;if(hr(t)){let u;try{let d=t.headers.get("Content-Type");d&&/\bapplication\/json\b/.test(d)?t.body==null?u=null:u=await t.json():u=await t.text()}catch(d){return{type:"error",error:d}}return r==="error"?{type:"error",error:new Ie(t.status,t.statusText,u),statusCode:t.status,headers:t.headers}:{type:"data",data:u,statusCode:t.status,headers:t.headers}}return r==="error"?nr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(a=t.init)==null?void 0:a.status,headers:(n=t.init)!=null&&n.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Ie(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:Be(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Be(t)?t.status:void 0}:nr(t)?{type:"data",data:t.data,statusCode:(s=t.init)==null?void 0:s.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function ho(e,t,r,a,n){let o=e.headers.get("Location");if(H(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!fr.test(o)){let l=a.slice(0,a.findIndex(s=>s.route.id===r)+1);o=Zt(new URL(t.url),l,n,o),e.headers.set("Location",o)}return e}function qr(e,t,r){if(fr.test(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),o=Ee(n.pathname,r)!=null;if(n.origin===t.origin&&o)return n.pathname+n.search+n.hash}return e}function Je(e,t,r,a){let n=e.createURL(Dn(t)).toString(),o={signal:r};if(a&&we(a.formMethod)){let{formMethod:l,formEncType:s}=a;o.method=l.toUpperCase(),s==="application/json"?(o.headers=new Headers({"Content-Type":s}),o.body=JSON.stringify(a.json)):s==="text/plain"?o.body=a.text:s==="application/x-www-form-urlencoded"&&a.formData?o.body=rr(a.formData):o.body=a.formData}return new Request(n,o)}function rr(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,typeof a=="string"?a:a.name);return t}function Qr(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function mo(e,t,r,a=!1,n=!1){let o={},l=null,s,i=!1,u={},d=r&&ge(r[1])?r[1].error:void 0;return e.forEach(m=>{if(!(m.route.id in t))return;let h=m.route.id,v=t[h];if(H(!ze(v),"Cannot handle redirect results in processLoaderData"),ge(v)){let w=v.error;if(d!==void 0&&(w=d,d=void 0),l=l||{},n)l[h]=w;else{let S=He(e,h);l[S.route.id]==null&&(l[S.route.id]=w)}a||(o[h]=Sn),i||(i=!0,s=Be(v.error)?v.error.status:500),v.headers&&(u[h]=v.headers)}else o[h]=v.data,v.statusCode&&v.statusCode!==200&&!i&&(s=v.statusCode),v.headers&&(u[h]=v.headers)}),d!==void 0&&r&&(l={[r[0]]:d},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:s||200,loaderHeaders:u}}function Zr(e,t,r,a,n,o){let{loaderData:l,errors:s}=mo(t,r,a);return n.filter(i=>!i.matches||i.matches.some(u=>u.shouldLoad)).forEach(i=>{let{key:u,match:d,controller:m}=i,h=o[u];if(H(h,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(ge(h)){let v=He(e.matches,d==null?void 0:d.route.id);s&&s[v.route.id]||(s={...s,[v.route.id]:h.error}),e.fetchers.delete(u)}else if(ze(h))H(!1,"Unhandled fetcher revalidation redirect");else{let v=Oe(h.data);e.fetchers.set(u,v)}}),{loaderData:l,errors:s}}function en(e,t,r,a){let n=Object.entries(t).filter(([,o])=>o!==Sn).reduce((o,[l,s])=>(o[l]=s,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(n[l]=e[l]),a&&a.hasOwnProperty(l))break}return n}function tn(e){return e?ge(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function He(e,t){return(t?e.slice(0,e.findIndex(a=>a.route.id===t)+1):[...e]).reverse().find(a=>a.route.hasErrorBoundary===!0)||e[0]}function rn(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Se(e,{pathname:t,routeId:r,method:a,type:n,message:o}={}){let l="Unknown Server Error",s="Unknown @remix-run/router error";return e===400?(l="Bad Request",a&&t&&r?s=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:n==="invalid-body"&&(s="Unable to encode submission body")):e===403?(l="Forbidden",s=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",s=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",a&&t&&r?s=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(s=`Invalid request method "${a.toUpperCase()}"`)),new Ie(e||500,l,new Error(s),!0)}function wt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[a,n]=t[r];if(ze(n))return{key:a,result:n}}}function Dn(e){let t=typeof e=="string"?$e(e):e;return Fe({...t,hash:""})}function po(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function yo(e){return hr(e.result)&&to.has(e.result.status)}function ge(e){return e.type==="error"}function ze(e){return(e&&e.type)==="redirect"}function nr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function hr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function vo(e){return eo.has(e.toUpperCase())}function we(e){return Qa.has(e.toUpperCase())}function mr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function st(e,t){let r=typeof t=="string"?$e(t).search:t.search;if(e[e.length-1].route.index&&mr(r||""))return e[e.length-1];let a=wn(e);return a[a.length-1]}function nn(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:l}=e;if(!(!t||!r||!a)){if(n!=null)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};if(o!=null)return{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:l,text:void 0}}}function Kt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function go(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function ot(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function wo(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Oe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Eo(e,t){try{let r=e.sessionStorage.getItem(Rn);if(r){let a=JSON.parse(r);for(let[n,o]of Object.entries(a||{}))o&&Array.isArray(o)&&t.set(n,new Set(o||[]))}}catch{}}function bo(e,t){if(t.size>0){let r={};for(let[a,n]of t)r[a]=[...n];try{e.sessionStorage.setItem(Rn,JSON.stringify(r))}catch(a){ie(!1,`Failed to save applied view transitions in sessionStorage (${a}).`)}}}function Ro(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Ve=c.createContext(null);Ve.displayName="DataRouter";var Ge=c.createContext(null);Ge.displayName="DataRouterState";var pr=c.createContext({isTransitioning:!1});pr.displayName="ViewTransition";var yr=c.createContext(new Map);yr.displayName="Fetchers";var kt=c.createContext(null);kt.displayName="Await";var xe=c.createContext(null);xe.displayName="Navigation";var Ft=c.createContext(null);Ft.displayName="Location";var Le=c.createContext({outlet:null,matches:[],isDataRoute:!1});Le.displayName="Route";var vr=c.createContext(null);vr.displayName="RouteError";function So(e,{relative:t}={}){H(dt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=c.useContext(xe),{hash:n,pathname:o,search:l}=ft(e,{relative:t}),s=o;return r!=="/"&&(s=o==="/"?r:Me([r,o])),a.createHref({pathname:s,search:l,hash:n})}function dt(){return c.useContext(Ft)!=null}function Ce(){return H(dt(),"useLocation() may be used only in the context of a <Router> component."),c.useContext(Ft).location}var _n="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Tn(e){c.useContext(xe).static||c.useLayoutEffect(e)}function kn(){let{isDataRoute:e}=c.useContext(Le);return e?Ao():xo()}function xo(){H(dt(),"useNavigate() may be used only in the context of a <Router> component.");let e=c.useContext(Ve),{basename:t,navigator:r}=c.useContext(xe),{matches:a}=c.useContext(Le),{pathname:n}=Ce(),o=JSON.stringify(cr(a)),l=c.useRef(!1);return Tn(()=>{l.current=!0}),c.useCallback((i,u={})=>{if(ie(l.current,_n),!l.current)return;if(typeof i=="number"){r.go(i);return}let d=dr(i,JSON.parse(o),n,u.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Me([t,d.pathname])),(u.replace?r.replace:r.push)(d,u.state,u)},[t,r,o,n,e])}var On=c.createContext(null);function fl(){return c.useContext(On)}function Lo(e){let t=c.useContext(Le).outlet;return t&&c.createElement(On.Provider,{value:e},t)}function hl(){let{matches:e}=c.useContext(Le),t=e[e.length-1];return t?t.params:{}}function ft(e,{relative:t}={}){let{matches:r}=c.useContext(Le),{pathname:a}=Ce(),n=JSON.stringify(cr(r));return c.useMemo(()=>dr(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function Co(e,t,r,a){H(dt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n,static:o}=c.useContext(xe),{matches:l}=c.useContext(Le),s=l[l.length-1],i=s?s.params:{},u=s?s.pathname:"/",d=s?s.pathnameBase:"/",m=s&&s.route;{let L=m&&m.path||"";$n(u,!m||L.endsWith("*")||L.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${L}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${L}"> to <Route path="${L==="/"?"*":`${L}/*`}">.`)}let h=Ce(),v;v=h;let w=v.pathname||"/",S=w;if(d!=="/"){let L=d.replace(/^\//,"").split("/");S="/"+w.replace(/^\//,"").split("/").slice(L.length).join("/")}let b=!o&&r&&r.matches&&r.matches.length>0?r.matches:Pe(e,{pathname:S});return ie(m||b!=null,`No routes matched location "${v.pathname}${v.search}${v.hash}" `),ie(b==null||b[b.length-1].route.element!==void 0||b[b.length-1].route.Component!==void 0||b[b.length-1].route.lazy!==void 0,`Matched leaf route at location "${v.pathname}${v.search}${v.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),To(b&&b.map(L=>Object.assign({},L,{params:Object.assign({},i,L.params),pathname:Me([d,n.encodeLocation?n.encodeLocation(L.pathname).pathname:L.pathname]),pathnameBase:L.pathnameBase==="/"?d:Me([d,n.encodeLocation?n.encodeLocation(L.pathnameBase).pathname:L.pathnameBase])})),l,r,a)}function Po(){let e=In(),t=Be(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=c.createElement(c.Fragment,null,c.createElement("p",null,"💿 Hey developer 👋"),c.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",c.createElement("code",{style:o},"ErrorBoundary")," or"," ",c.createElement("code",{style:o},"errorElement")," prop on your route.")),c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),r?c.createElement("pre",{style:n},r):null,l)}var Mo=c.createElement(Po,null),Do=class extends c.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?c.createElement(Le.Provider,{value:this.props.routeContext},c.createElement(vr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function _o({routeContext:e,match:t,children:r}){let a=c.useContext(Ve);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),c.createElement(Le.Provider,{value:e},r)}function To(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,o=r==null?void 0:r.errors;if(o!=null){let i=n.findIndex(u=>u.route.id&&(o==null?void 0:o[u.route.id])!==void 0);H(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,i+1))}let l=!1,s=-1;if(r)for(let i=0;i<n.length;i++){let u=n[i];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(s=i),u.route.id){let{loaderData:d,errors:m}=r,h=u.route.loader&&!d.hasOwnProperty(u.route.id)&&(!m||m[u.route.id]===void 0);if(u.route.lazy||h){l=!0,s>=0?n=n.slice(0,s+1):n=[n[0]];break}}}return n.reduceRight((i,u,d)=>{let m,h=!1,v=null,w=null;r&&(m=o&&u.route.id?o[u.route.id]:void 0,v=u.route.errorElement||Mo,l&&(s<0&&d===0?($n("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,w=null):s===d&&(h=!0,w=u.route.hydrateFallbackElement||null)));let S=t.concat(n.slice(0,d+1)),b=()=>{let R;return m?R=v:h?R=w:u.route.Component?R=c.createElement(u.route.Component,null):u.route.element?R=u.route.element:R=i,c.createElement(_o,{match:u,routeContext:{outlet:i,matches:S,isDataRoute:r!=null},children:R})};return r&&(u.route.ErrorBoundary||u.route.errorElement||d===0)?c.createElement(Do,{location:r.location,revalidation:r.revalidation,component:v,error:m,children:b(),routeContext:{outlet:null,matches:S,isDataRoute:!0}}):b()},null)}function gr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ko(e){let t=c.useContext(Ve);return H(t,gr(e)),t}function Xe(e){let t=c.useContext(Ge);return H(t,gr(e)),t}function Oo(e){let t=c.useContext(Le);return H(t,gr(e)),t}function ht(e){let t=Oo(e),r=t.matches[t.matches.length-1];return H(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Fo(){return ht("useRouteId")}function Io(){return Xe("useNavigation").navigation}function Fn(){let{matches:e,loaderData:t}=Xe("useMatches");return c.useMemo(()=>e.map(r=>pn(r,t)),[e,t])}function ml(){let e=Xe("useLoaderData"),t=ht("useLoaderData");return e.loaderData[t]}function pl(e){return Xe("useRouteLoaderData").loaderData[e]}function yl(){let e=Xe("useActionData"),t=ht("useLoaderData");return e.actionData?e.actionData[t]:void 0}function In(){var a;let e=c.useContext(vr),t=Xe("useRouteError"),r=ht("useRouteError");return e!==void 0?e:(a=t.errors)==null?void 0:a[r]}function $o(){let e=c.useContext(kt);return e==null?void 0:e._data}function Ao(){let{router:e}=ko("useNavigate"),t=ht("useNavigate"),r=c.useRef(!1);return Tn(()=>{r.current=!0}),c.useCallback(async(n,o={})=>{ie(r.current,_n),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...o}))},[e,t])}var an={};function $n(e,t,r){!t&&!an[e]&&(an[e]=!0,ie(!1,r))}var on={};function ln(e,t){!e&&!on[t]&&(on[t]=!0,console.warn(t))}function vl(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&ie(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:c.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&ie(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:c.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&ie(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:c.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var gl=["HydrateFallback","hydrateFallbackElement"],No=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function wl({router:e,flushSync:t}){let[r,a]=c.useState(e.state),[n,o]=c.useState(),[l,s]=c.useState({isTransitioning:!1}),[i,u]=c.useState(),[d,m]=c.useState(),[h,v]=c.useState(),w=c.useRef(new Map),S=c.useCallback((_,{deletedFetchers:D,flushSync:C,viewTransitionOpts:p})=>{_.fetchers.forEach((j,A)=>{j.data!==void 0&&w.current.set(A,j.data)}),D.forEach(j=>w.current.delete(j)),ln(C===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let k=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(ln(p==null||k,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!p||!k){t&&C?t(()=>a(_)):c.startTransition(()=>a(_));return}if(t&&C){t(()=>{d&&(i&&i.resolve(),d.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:p.currentLocation,nextLocation:p.nextLocation})});let j=e.window.document.startViewTransition(()=>{t(()=>a(_))});j.finished.finally(()=>{t(()=>{u(void 0),m(void 0),o(void 0),s({isTransitioning:!1})})}),t(()=>m(j));return}d?(i&&i.resolve(),d.skipTransition(),v({state:_,currentLocation:p.currentLocation,nextLocation:p.nextLocation})):(o(_),s({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}))},[e.window,t,d,i]);c.useLayoutEffect(()=>e.subscribe(S),[e,S]),c.useEffect(()=>{l.isTransitioning&&!l.flushSync&&u(new No)},[l]),c.useEffect(()=>{if(i&&n&&e.window){let _=n,D=i.promise,C=e.window.document.startViewTransition(async()=>{c.startTransition(()=>a(_)),await D});C.finished.finally(()=>{u(void 0),m(void 0),o(void 0),s({isTransitioning:!1})}),m(C)}},[n,i,e.window]),c.useEffect(()=>{i&&n&&r.location.key===n.location.key&&i.resolve()},[i,d,r.location,n]),c.useEffect(()=>{!l.isTransitioning&&h&&(o(h.state),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),v(void 0))},[l.isTransitioning,h]);let b=c.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:_=>e.navigate(_),push:(_,D,C)=>e.navigate(_,{state:D,preventScrollReset:C==null?void 0:C.preventScrollReset}),replace:(_,D,C)=>e.navigate(_,{replace:!0,state:D,preventScrollReset:C==null?void 0:C.preventScrollReset})}),[e]),R=e.basename||"/",L=c.useMemo(()=>({router:e,navigator:b,static:!1,basename:R}),[e,b,R]);return c.createElement(c.Fragment,null,c.createElement(Ve.Provider,{value:L},c.createElement(Ge.Provider,{value:r},c.createElement(yr.Provider,{value:w.current},c.createElement(pr.Provider,{value:l},c.createElement(Ho,{basename:R,location:r.location,navigationType:r.historyAction,navigator:b},c.createElement(jo,{routes:e.routes,future:e.future,state:r})))))),null)}var jo=c.memo(Uo);function Uo({routes:e,future:t,state:r}){return Co(e,void 0,r,t)}function El(e){return Lo(e.context)}function Ho({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){H(!dt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=c.useMemo(()=>({basename:l,navigator:n,static:o,future:{}}),[l,n,o]);typeof r=="string"&&(r=$e(r));let{pathname:i="/",search:u="",hash:d="",state:m=null,key:h="default"}=r,v=c.useMemo(()=>{let w=Ee(i,l);return w==null?null:{location:{pathname:w,search:u,hash:d,state:m,key:h},navigationType:a}},[l,i,u,d,m,h,a]);return ie(v!=null,`<Router basename="${l}"> is not able to match the URL "${i}${u}${d}" because it does not start with the basename, so the <Router> won't render anything.`),v==null?null:c.createElement(xe.Provider,{value:s},c.createElement(Ft.Provider,{children:t,value:v}))}function bl({children:e,errorElement:t,resolve:r}){return c.createElement(zo,{resolve:r,errorElement:t},c.createElement(Bo,null,e))}var zo=class extends c.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:e,errorElement:t,resolve:r}=this.props,a=null,n=0;if(!(r instanceof Promise))n=1,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>r});else if(this.state.error){n=2;let o=this.state.error;a=Promise.reject().catch(()=>{}),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>o})}else r._tracked?(a=r,n="_error"in a?2:"_data"in a?1:0):(n=0,Object.defineProperty(r,"_tracked",{get:()=>!0}),a=r.then(o=>Object.defineProperty(r,"_data",{get:()=>o}),o=>Object.defineProperty(r,"_error",{get:()=>o})));if(n===2&&!t)throw a._error;if(n===2)return c.createElement(kt.Provider,{value:a,children:t});if(n===1)return c.createElement(kt.Provider,{value:a,children:e});throw a}};function Bo({children:e}){let t=$o(),r=typeof e=="function"?e(t):e;return c.createElement(c.Fragment,null,r)}var Lt="get",Ct="application/x-www-form-urlencoded";function It(e){return e!=null&&typeof e.tagName=="string"}function Vo(e){return It(e)&&e.tagName.toLowerCase()==="button"}function Wo(e){return It(e)&&e.tagName.toLowerCase()==="form"}function Yo(e){return It(e)&&e.tagName.toLowerCase()==="input"}function Jo(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Ko(e,t){return e.button===0&&(!t||t==="_self")&&!Jo(e)}function ar(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let a=e[r];return t.concat(Array.isArray(a)?a.map(n=>[r,n]):[[r,a]])},[]))}function Go(e,t){let r=ar(e);return t&&t.forEach((a,n)=>{r.has(n)||t.getAll(n).forEach(o=>{r.append(n,o)})}),r}var Et=null;function Xo(){if(Et===null)try{new FormData(document.createElement("form"),0),Et=!1}catch{Et=!0}return Et}var qo=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Gt(e){return e!=null&&!qo.has(e)?(ie(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ct}"`),null):e}function Qo(e,t){let r,a,n,o,l;if(Wo(e)){let s=e.getAttribute("action");a=s?Ee(s,t):null,r=e.getAttribute("method")||Lt,n=Gt(e.getAttribute("enctype"))||Ct,o=new FormData(e)}else if(Vo(e)||Yo(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||s.getAttribute("action");if(a=i?Ee(i,t):null,r=e.getAttribute("formmethod")||s.getAttribute("method")||Lt,n=Gt(e.getAttribute("formenctype"))||Gt(s.getAttribute("enctype"))||Ct,o=new FormData(s,e),!Xo()){let{name:u,type:d,value:m}=e;if(d==="image"){let h=u?`${u}.`:"";o.append(`${h}x`,"0"),o.append(`${h}y`,"0")}else u&&o.append(u,m)}}else{if(It(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Lt,a=null,n=Ct,l=e}return o&&n==="text/plain"&&(l=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:l}}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function An(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Zo(e,t,r){let a=e.map(o=>{var i;let l=t[o.route.id],s=r.routes[o.route.id];return[s&&s.css?s.css.map(u=>({rel:"stylesheet",href:u})):[],((i=l==null?void 0:l.links)==null?void 0:i.call(l))||[]]}).flat(2),n=Er(e,r);return Hn(a,n)}function Nn(e){return e.css?e.css.map(t=>({rel:"stylesheet",href:t})):[]}async function ei(e){if(!e.css)return;let t=Nn(e);await Promise.all(t.map(Un))}async function jn(e,t){if(!e.css&&!t.links||!oi())return;let r=[];if(e.css&&r.push(...Nn(e)),t.links&&r.push(...t.links()),r.length===0)return;let a=[];for(let n of r)!wr(n)&&n.rel==="stylesheet"&&a.push({...n,rel:"preload",as:"style"});await Promise.all(a.map(Un))}async function Un(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");Object.assign(r,e);function a(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{a(),t()},r.onerror=()=>{a(),t()},document.head.appendChild(r)})}function wr(e){return e!=null&&typeof e.page=="string"}function ti(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ri(e,t,r){let a=await Promise.all(e.map(async n=>{let o=t.routes[n.route.id];if(o){let l=await An(o,r);return l.links?l.links():[]}return[]}));return Hn(a.flat(1).filter(ti).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function sn(e,t,r,a,n,o){let l=(i,u)=>r[u]?i.route.id!==r[u].route.id:!0,s=(i,u)=>{var d;return r[u].pathname!==i.pathname||((d=r[u].route.path)==null?void 0:d.endsWith("*"))&&r[u].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,u)=>l(i,u)||s(i,u)):o==="data"?t.filter((i,u)=>{var m;let d=a.routes[i.route.id];if(!d||!d.hasLoader)return!1;if(l(i,u)||s(i,u))return!0;if(i.route.shouldRevalidate){let h=i.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:((m=r[0])==null?void 0:m.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function Er(e,t,{includeHydrateFallback:r}={}){return ni(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let o=[n.module];return n.clientActionModule&&(o=o.concat(n.clientActionModule)),n.clientLoaderModule&&(o=o.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(o=o.concat(n.hydrateFallbackModule)),n.imports&&(o=o.concat(n.imports)),o}).flat(1))}function ni(e){return[...new Set(e)]}function ai(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function Hn(e,t){let r=new Set,a=new Set(t);return e.reduce((n,o)=>{if(t&&!wr(o)&&o.as==="script"&&o.href&&a.has(o.href))return n;let s=JSON.stringify(ai(o));return r.has(s)||(r.add(s),n.push({key:s,link:o})),n},[])}var bt;function oi(){if(bt!==void 0)return bt;let e=document.createElement("link");return bt=e.relList.supports("preload"),e=null,bt}function un(e){return{__html:e}}var ii=-1,li=-2,si=-3,ui=-4,ci=-5,di=-6,fi=-7,hi="B",mi="D",zn="E",pi="M",yi="N",Bn="P",vi="R",gi="S",wi="Y",Ei="U",bi="Z",Vn=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function Ri(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,a){const n=e.decode(r,{stream:!0}),o=(t+n).split(`
`);t=o.pop()||"";for(const l of o)a.enqueue(l)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Xt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function or(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return cn.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const a=r.length;for(const n of e)r.push(n);return t.length=r.length,cn.call(this,a)}function cn(e){const{hydrated:t,values:r,deferred:a,plugins:n}=this;let o;const l=[[e,i=>{o=i}]];let s=[];for(;l.length>0;){const[i,u]=l.pop();switch(i){case fi:u(void 0);continue;case ci:u(null);continue;case li:u(NaN);continue;case di:u(1/0);continue;case si:u(-1/0);continue;case ui:u(-0);continue}if(t[i]){u(t[i]);continue}const d=r[i];if(!d||typeof d!="object"){t[i]=d,u(d);continue}if(Array.isArray(d))if(typeof d[0]=="string"){const[m,h,v]=d;switch(m){case mi:u(t[i]=new Date(h));continue;case Ei:u(t[i]=new URL(h));continue;case hi:u(t[i]=BigInt(h));continue;case vi:u(t[i]=new RegExp(h,v));continue;case wi:u(t[i]=Symbol.for(h));continue;case gi:const w=new Set;t[i]=w;for(let D=d.length-1;D>0;D--)l.push([d[D],C=>{w.add(C)}]);u(w);continue;case pi:const S=new Map;t[i]=S;for(let D=d.length-2;D>0;D-=2){const C=[];l.push([d[D+1],p=>{C[1]=p}]),l.push([d[D],p=>{C[0]=p}]),s.push(()=>{S.set(C[0],C[1])})}u(S);continue;case yi:const b=Object.create(null);t[i]=b;for(const D of Object.keys(h).reverse()){const C=[];l.push([h[D],p=>{C[1]=p}]),l.push([Number(D.slice(1)),p=>{C[0]=p}]),s.push(()=>{b[C[0]]=C[1]})}u(b);continue;case Bn:if(t[h])u(t[i]=t[h]);else{const D=new Vn;a[h]=D,u(t[i]=D.promise)}continue;case zn:const[,R,L]=d;let _=L&&Xt&&Xt[L]?new Xt[L](R):new Error(R);t[i]=_,u(_);continue;case bi:u(t[i]=t[h]);continue;default:if(Array.isArray(n)){const D=[],C=d.slice(1);for(let p=0;p<C.length;p++){const k=C[p];l.push([k,j=>{D[p]=j}])}s.push(()=>{for(const p of n){const k=p(d[0],...D);if(k){u(t[i]=k.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const m=[];t[i]=m;for(let h=0;h<d.length;h++){const v=d[h];v!==ii&&l.push([v,w=>{m[h]=w}])}u(m);continue}else{const m={};t[i]=m;for(const h of Object.keys(d).reverse()){const v=[];l.push([d[h],w=>{v[1]=w}]),l.push([Number(h.slice(1)),w=>{v[0]=w}]),s.push(()=>{m[v[0]]=v[1]})}u(m);continue}}for(;s.length>0;)s.pop()();return o}async function Si(e,t){const{plugins:r}=t??{},a=new Vn,n=e.pipeThrough(Ri()).getReader(),o={values:[],hydrated:[],deferred:{},plugins:r},l=await xi.call(o,n);let s=a.promise;return l.done?a.resolve():s=Li.call(o,n).then(a.resolve).catch(i=>{for(const u of Object.values(o.deferred))u.reject(i);a.reject(i)}),{done:s.then(()=>n.closed),value:l.value}}async function xi(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:or.call(this,r)}}async function Li(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case Bn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=or.call(this,s);o.resolve(i);break}case zn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=or.call(this,s);o.reject(i);break}default:throw new SyntaxError}t=await e.read()}}async function Ci(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var ir=Symbol("SingleFetchRedirect"),Wn=class extends Error{},Pi=202,Yn=new Set([100,101,204,205]);function Rl(e,t,r,a,n){let o=Mi(e,l=>{let s=t.routes[l.route.id];ye(s,"Route not found in manifest");let i=r[l.route.id];return{hasLoader:s.hasLoader,hasClientLoader:s.hasClientLoader,hasShouldRevalidate:!!(i!=null&&i.shouldRevalidate)}},Ii,a,n);return async l=>l.unstable_runClientMiddleware(o)}function Mi(e,t,r,a,n){return async o=>{let{request:l,matches:s,fetcherKey:i}=o,u=e();if(l.method!=="GET")return Di(o,r,n);let d=s.some(m=>{let{hasLoader:h,hasClientLoader:v}=t(m);return m.unstable_shouldCallHandler()&&h&&!v});return!a&&!d?_i(o,t,r,n):i?Oi(o,r,n):Ti(o,u,t,r,a,n)}}async function Di(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());ye(a,"No action match found");let n,o=await a.resolve(async l=>await l(async()=>{let{data:i,status:u}=await t(e,r,[a.route.id]);return n=u,ct(i,a.route.id)}));return hr(o.result)||Be(o.result)||nr(o.result)?{[a.route.id]:o}:{[a.route.id]:{type:o.type,result:Xa(o.result,n)}}}async function _i(e,t,r,a){let n=e.matches.filter(l=>l.unstable_shouldCallHandler()),o={};return await Promise.all(n.map(l=>l.resolve(async s=>{try{let{hasClientLoader:i}=t(l),u=l.route.id,d=i?await s(async()=>{let{data:m}=await r(e,a,[u]);return ct(m,u)}):await s();o[l.route.id]={type:"data",result:d}}catch(i){o[l.route.id]={type:"error",result:i}}}))),o}async function Ti(e,t,r,a,n,o){let l=new Set,s=!1,i=e.matches.map(()=>dn()),u=dn(),d={},m=Promise.all(e.matches.map(async(v,w)=>v.resolve(async S=>{i[w].resolve();let b=v.route.id,{hasLoader:R,hasClientLoader:L,hasShouldRevalidate:_}=r(v),D=!v.unstable_shouldRevalidateArgs||v.unstable_shouldRevalidateArgs.actionStatus==null||v.unstable_shouldRevalidateArgs.actionStatus<400;if(!v.unstable_shouldCallHandler(D)){s||(s=v.unstable_shouldRevalidateArgs!=null&&R&&_===!0);return}if(L){R&&(s=!0);try{let p=await S(async()=>{let{data:k}=await a(e,o,[b]);return ct(k,b)});d[b]={type:"data",result:p}}catch(p){d[b]={type:"error",result:p}}return}R&&l.add(b);try{let p=await S(async()=>{let k=await u.promise;return ct(k,b)});d[b]={type:"data",result:p}}catch(p){d[b]={type:"error",result:p}}})));if(await Promise.all(i.map(v=>v.promise)),(!t.state.initialized&&t.state.navigation.state==="idle"||l.size===0)&&!window.__reactRouterHdrActive)u.resolve({routes:{}});else{let v=n&&s&&l.size>0?[...l.keys()]:void 0;try{let w=await a(e,o,v);u.resolve(w.data)}catch(w){u.reject(w)}}return await m,await ki(u.promise,e.matches,l,d),d}async function ki(e,t,r,a){try{let n,o=await e;if("routes"in o){for(let l of t)if(l.route.id in o.routes){let s=o.routes[l.route.id];if("error"in s){n=s.error;break}}}n!==void 0&&Array.from(r.values()).forEach(l=>{a[l].result instanceof Wn&&(a[l].result=n)})}catch{}}async function Oi(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());ye(a,"No fetcher match found");let n=a.route.id,o=await a.resolve(async l=>l(async()=>{let{data:s}=await t(e,r,[n]);return ct(s,n)}));return{[a.route.id]:o}}function Fi(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let a of t)a&&r.push(a);for(let a of r)e.searchParams.append("index",a);return e}function Jn(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&Ee(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}async function Ii(e,t,r){let{request:a}=e,n=Jn(a.url,t);a.method==="GET"&&(n=Fi(n),r&&n.searchParams.set("_routes",r.join(",")));let o=await fetch(n,await Ci(a));if(o.status===404&&!o.headers.has("X-Remix-Response"))throw new Ie(404,"Not Found",!0);if(o.status===204&&o.headers.has("X-Remix-Redirect"))return{status:Pi,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:o.headers.get("X-Remix-Revalidate")==="true",reload:o.headers.get("X-Remix-Reload-Document")==="true",replace:o.headers.get("X-Remix-Replace")==="true"}}};if(Yn.has(o.status)){let l={};return r&&a.method!=="GET"&&(l[r[0]]={data:void 0}),{status:o.status,data:{routes:l}}}ye(o.body,"No response body to decode");try{let l=await $i(o.body,window),s;if(a.method==="GET"){let i=l.value;ir in i?s={redirect:i[ir]}:s={routes:i}}else{let i=l.value,u=r==null?void 0:r[0];ye(u,"No routeId found for single fetch call decoding"),"redirect"in i?s={redirect:i}:s={routes:{[u]:i}}}return{status:o.status,data:s}}catch{throw new Error("Unable to decode turbo-stream response")}}function $i(e,t){return Si(e,{plugins:[(r,...a)=>{if(r==="SanitizedError"){let[n,o,l]=a,s=Error;n&&n in t&&typeof t[n]=="function"&&(s=t[n]);let i=new s(o);return i.stack=l,{value:i}}if(r==="ErrorResponse"){let[n,o,l]=a;return{value:new Ie(o,l,n)}}if(r==="SingleFetchRedirect")return{value:{[ir]:a[0]}};if(r==="SingleFetchClassInstance")return{value:a[0]};if(r==="SingleFetchFallback")return{value:void 0}}]})}function ct(e,t){if("redirect"in e){let{redirect:a,revalidate:n,reload:o,replace:l,status:s}=e.redirect;throw qa(a,{status:s,headers:{...n?{"X-Remix-Revalidate":"yes"}:null,...o?{"X-Remix-Reload-Document":"yes"}:null,...l?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if(r==null)throw new Wn(`No result found for routeId "${t}"`);if("error"in r)throw r.error;if("data"in r)return r.data;throw new Error(`Invalid response found for routeId "${t}"`)}function dn(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Sl=class extends c.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?c.createElement(Kn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function Kn({error:e,isOutsideRemixApp:t}){console.error(e);let r=c.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."
        );
      `}});if(Be(e))return c.createElement(lr,{title:"Unhandled Thrown Response!"},c.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let a;if(e instanceof Error)a=e;else{let n=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);a=new Error(n)}return c.createElement(lr,{title:"Application Error!",isOutsideRemixApp:t},c.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),c.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),r)}function lr({title:e,renderScripts:t,isOutsideRemixApp:r,children:a}){var o;let{routeModules:n}=qe();return(o=n.root)!=null&&o.Layout&&!r?a:c.createElement("html",{lang:"en"},c.createElement("head",null,c.createElement("meta",{charSet:"utf-8"}),c.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),c.createElement("title",null,e)),c.createElement("body",null,c.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},a,t?c.createElement(qi,null):null)))}function Ai(){return c.createElement(lr,{title:"Loading...",renderScripts:!0},c.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            `}}))}function Gn(e){let t={};return Object.values(e).forEach(r=>{if(r){let a=r.parentId||"";t[a]||(t[a]=[]),t[a].push(r)}}),t}function Ni(e,t,r){let a=Xn(t),n=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?Ai:void 0,o=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>c.createElement(Kn,{error:In()}):void 0;return e.id==="root"&&t.Layout?{...a?{element:c.createElement(t.Layout,null,c.createElement(a,null))}:{Component:a},...o?{errorElement:c.createElement(t.Layout,null,c.createElement(o,null))}:{ErrorBoundary:o},...n?{hydrateFallbackElement:c.createElement(t.Layout,null,c.createElement(n,null))}:{HydrateFallback:n}}:{Component:a,ErrorBoundary:o,HydrateFallback:n}}function xl(e,t,r,a,n,o){return br(t,r,a,n,o,"",Gn(t),e)}function Rt(e,t){if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction){let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(a),new Ie(400,"Bad Request",new Error(a),!0)}}function qt(e,t){let r=e==="clientAction"?"a":"an",a=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(a),new Ie(405,"Method Not Allowed",new Error(a),!0)}function br(e,t,r,a,n,o="",l=Gn(e),s){return(l[o]||[]).map(i=>{var L,_,D;let u=t[i.id];function d(C){return ye(typeof C=="function","No single fetch function available for route handler"),C()}function m(C){return i.hasLoader?d(C):Promise.resolve(null)}function h(C){if(!i.hasAction)throw qt("action",i.id);return d(C)}function v(C){import(C)}function w(C){C.clientActionModule&&v(C.clientActionModule),C.clientLoaderModule&&v(C.clientLoaderModule)}async function S(C){let p=t[i.id],k=p?jn(i,p):Promise.resolve();try{return C()}finally{await k}}let b={id:i.id,index:i.index,path:i.path};if(u){Object.assign(b,{...b,...Ni(i,u,n),unstable_middleware:u.unstable_clientMiddleware,handle:u.handle,shouldRevalidate:fn(b.path,u,i,a,s)});let C=r&&r.loaderData&&i.id in r.loaderData,p=C?(L=r==null?void 0:r.loaderData)==null?void 0:L[i.id]:void 0,k=r&&r.errors&&i.id in r.errors,j=k?(_=r==null?void 0:r.errors)==null?void 0:_[i.id]:void 0,A=s==null&&(((D=u.clientLoader)==null?void 0:D.hydrate)===!0||!i.hasLoader);b.loader=async({request:q,params:ae,context:fe},V)=>{try{return await S(async()=>(ye(u,"No `routeModule` available for critical-route loader"),u.clientLoader?u.clientLoader({request:q,params:ae,context:fe,async serverLoader(){if(Rt("loader",i),A){if(C)return p;if(k)throw j}return m(V)}}):m(V)))}finally{A=!1}},b.loader.hydrate=qn(i.id,u.clientLoader,i.hasLoader,n),b.action=({request:q,params:ae,context:fe},V)=>S(async()=>{if(ye(u,"No `routeModule` available for critical-route action"),!u.clientAction){if(n)throw qt("clientAction",i.id);return h(V)}return u.clientAction({request:q,params:ae,context:fe,async serverAction(){return Rt("action",i),h(V)}})})}else{i.hasClientLoader||(b.loader=(k,j)=>S(()=>m(j))),i.hasClientAction||(b.action=(k,j)=>S(()=>{if(n)throw qt("clientAction",i.id);return h(j)}));let C;async function p(){return C?await C:(C=(async()=>{(i.clientLoaderModule||i.clientActionModule)&&await new Promise(j=>setTimeout(j,0));let k=Ui(i,t);return w(i),await k})(),await C)}b.lazy={loader:i.hasClientLoader?async()=>{let{clientLoader:k}=i.clientLoaderModule?await import(i.clientLoaderModule):await p();return ye(k,"No `clientLoader` export found"),(j,A)=>k({...j,async serverLoader(){return Rt("loader",i),m(A)}})}:void 0,action:i.hasClientAction?async()=>{let k=i.clientActionModule?import(i.clientActionModule):p();w(i);let{clientAction:j}=await k;return ye(j,"No `clientAction` export found"),(A,q)=>j({...A,async serverAction(){return Rt("action",i),h(q)}})}:void 0,unstable_middleware:i.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:k}=i.clientMiddlewareModule?await import(i.clientMiddlewareModule):await p();return ye(k,"No `unstable_clientMiddleware` export found"),k}:void 0,shouldRevalidate:async()=>{let k=await p();return fn(b.path,k,i,a,s)},handle:async()=>(await p()).handle,Component:async()=>(await p()).Component,ErrorBoundary:i.hasErrorBoundary?async()=>(await p()).ErrorBoundary:void 0}}let R=br(e,t,r,a,n,i.id,l,s);return R.length>0&&(b.children=R),b})}function fn(e,t,r,a,n){if(n)return ji(r.id,t.shouldRevalidate,n);if(!a&&r.hasLoader&&!r.hasClientLoader){let o=e?gn(e)[1].map(s=>s.paramName):[];const l=s=>o.some(i=>s.currentParams[i]!==s.nextParams[i]);if(t.shouldRevalidate){let s=t.shouldRevalidate;return i=>s({...i,defaultShouldRevalidate:l(i)})}else return s=>l(s)}if(a&&t.shouldRevalidate){let o=t.shouldRevalidate;return l=>o({...l,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function ji(e,t,r){let a=!1;return n=>a?t?t(n):n.defaultShouldRevalidate:(a=!0,r.has(e))}async function Ui(e,t){let r=An(e,t),a=ei(e),n=await r;return await Promise.all([a,jn(e,n)]),{Component:Xn(n),ErrorBoundary:n.ErrorBoundary,unstable_clientMiddleware:n.unstable_clientMiddleware,clientAction:n.clientAction,clientLoader:n.clientLoader,handle:n.handle,links:n.links,meta:n.meta,shouldRevalidate:n.shouldRevalidate}}function Xn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function qn(e,t,r,a){return a&&e!=="root"||t!=null&&(t.hydrate===!0||r!==!0)}var Pt=new Set,Hi=1e3,Ot=new Set,zi=7680;function Rr(e,t){return e.mode==="lazy"&&t===!0}function Bi({sri:e,...t},r){let a=new Set(r.state.matches.map(s=>s.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(n.pop();n.length>0;)o.push(`/${n.join("/")}`),n.pop();o.forEach(s=>{let i=Pe(r.routes,s,r.basename);i&&i.forEach(u=>a.add(u.route.id))});let l=[...a].reduce((s,i)=>Object.assign(s,{[i]:t.routes[i]}),{});return{...t,routes:l,sri:e?!0:void 0}}function Ll(e,t,r,a,n,o){if(Rr(a,r))return async({path:l,patch:s,signal:i,fetcherKey:u})=>{Ot.has(l)||await Qn([l],u?window.location.href:l,e,t,r,n,o,a.manifestPath,s,i)}}function Cl(e,t,r,a,n,o){c.useEffect(()=>{var d,m;if(!Rr(n,a)||((m=(d=window.navigator)==null?void 0:d.connection)==null?void 0:m.saveData)===!0)return;function l(h){let v=h.tagName==="FORM"?h.getAttribute("action"):h.getAttribute("href");if(!v)return;let w=h.tagName==="A"?h.pathname:new URL(v,window.location.origin).pathname;Ot.has(w)||Pt.add(w)}async function s(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let h=Array.from(Pt.keys()).filter(v=>Ot.has(v)?(Pt.delete(v),!1):!0);if(h.length!==0)try{await Qn(h,null,t,r,a,o,e.basename,n.manifestPath,e.patchRoutes)}catch(v){console.error("Failed to fetch manifest patches",v)}}let i=Yi(s,100);s();let u=new MutationObserver(()=>i());return u.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>u.disconnect()},[a,o,t,r,e,n])}function Vi(e,t){let r=e||"/__manifest";return t==null?r:`${t}${r}`.replace(/\/+/g,"/")}var Qt="react-router-manifest-version";async function Qn(e,t,r,a,n,o,l,s,i,u){let d=new URL(Vi(s,l),window.location.origin);if(e.sort().forEach(S=>d.searchParams.append("p",S)),d.searchParams.set("version",r.version),d.toString().length>zi){Pt.clear();return}let m;try{let S=await fetch(d,{signal:u});if(S.ok){if(S.status===204&&S.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(Qt)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}sessionStorage.setItem(Qt,r.version),window.location.href=t,console.warn("Detected manifest version mismatch, reloading..."),await new Promise(()=>{})}else if(S.status>=400)throw new Error(await S.text())}else throw new Error(`${S.status} ${S.statusText}`);sessionStorage.removeItem(Qt),m=await S.json()}catch(S){if(u!=null&&u.aborted)return;throw S}let h=new Set(Object.keys(r.routes)),v=Object.values(m).reduce((S,b)=>(b&&!h.has(b.id)&&(S[b.id]=b),S),{});Object.assign(r.routes,v),e.forEach(S=>Wi(S,Ot));let w=new Set;Object.values(v).forEach(S=>{S&&(!S.parentId||!v[S.parentId])&&w.add(S.parentId)}),w.forEach(S=>i(S||null,br(v,a,null,n,o,S)))}function Wi(e,t){if(t.size>=Hi){let r=t.values().next().value;t.delete(r)}t.add(e)}function Yi(e,t){let r;return(...a)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...a),t)}}function Sr(){let e=c.useContext(Ve);return ye(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function $t(){let e=c.useContext(Ge);return ye(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var At=c.createContext(void 0);At.displayName="FrameworkContext";function qe(){let e=c.useContext(At);return ye(e,"You must render this element inside a <HydratedRouter> element"),e}function Ji(e,t){let r=c.useContext(At),[a,n]=c.useState(!1),[o,l]=c.useState(!1),{onFocus:s,onBlur:i,onMouseEnter:u,onMouseLeave:d,onTouchStart:m}=t,h=c.useRef(null);c.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let S=R=>{R.forEach(L=>{l(L.isIntersecting)})},b=new IntersectionObserver(S,{threshold:.5});return h.current&&b.observe(h.current),()=>{b.disconnect()}}},[e]),c.useEffect(()=>{if(a){let S=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(S)}}},[a]);let v=()=>{n(!0)},w=()=>{n(!1),l(!1)};return r?e!=="intent"?[o,h,{}]:[o,h,{onFocus:it(s,v),onBlur:it(i,w),onMouseEnter:it(u,v),onMouseLeave:it(d,w),onTouchStart:it(m,v)}]:[!1,h,{}]}function it(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function xr(e,t,r){if(r&&!Mt)return[e[0]];if(t){let a=e.findIndex(n=>t[n.route.id]!==void 0);return e.slice(0,a+1)}return e}function Pl(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:a}=qe(),{errors:n,matches:o}=$t(),l=xr(o,n,e),s=c.useMemo(()=>Zo(l,r,t),[l,r,t]);return c.createElement(c.Fragment,null,typeof a=="string"?c.createElement("style",{dangerouslySetInnerHTML:{__html:a}}):null,typeof a=="object"?c.createElement("link",{rel:"stylesheet",href:a.href}):null,s.map(({key:i,link:u})=>wr(u)?c.createElement(Zn,{key:i,...u}):c.createElement("link",{key:i,...u})))}function Zn({page:e,...t}){let{router:r}=Sr(),a=c.useMemo(()=>Pe(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?c.createElement(Gi,{page:e,matches:a,...t}):null}function Ki(e){let{manifest:t,routeModules:r}=qe(),[a,n]=c.useState([]);return c.useEffect(()=>{let o=!1;return ri(e,t,r).then(l=>{o||n(l)}),()=>{o=!0}},[e,t,r]),a}function Gi({page:e,matches:t,...r}){let a=Ce(),{manifest:n,routeModules:o}=qe(),{basename:l}=Sr(),{loaderData:s,matches:i}=$t(),u=c.useMemo(()=>sn(e,t,i,n,a,"data"),[e,t,i,n,a]),d=c.useMemo(()=>sn(e,t,i,n,a,"assets"),[e,t,i,n,a]),m=c.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let w=new Set,S=!1;if(t.forEach(R=>{var _;let L=n.routes[R.route.id];!L||!L.hasLoader||(!u.some(D=>D.route.id===R.route.id)&&R.route.id in s&&((_=o[R.route.id])!=null&&_.shouldRevalidate)||L.hasClientLoader?S=!0:w.add(R.route.id))}),w.size===0)return[];let b=Jn(e,l);return S&&w.size>0&&b.searchParams.set("_routes",t.filter(R=>w.has(R.route.id)).map(R=>R.route.id).join(",")),[b.pathname+b.search]},[l,s,a,n,u,t,e,o]),h=c.useMemo(()=>Er(d,n),[d,n]),v=Ki(d);return c.createElement(c.Fragment,null,m.map(w=>c.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...r})),h.map(w=>c.createElement("link",{key:w,rel:"modulepreload",href:w,...r})),v.map(({key:w,link:S})=>c.createElement("link",{key:w,...S})))}function Ml(){let{isSpaMode:e,routeModules:t}=qe(),{errors:r,matches:a,loaderData:n}=$t(),o=Ce(),l=xr(a,r,e),s=null;r&&(s=r[l[l.length-1].route.id]);let i=[],u=null,d=[];for(let m=0;m<l.length;m++){let h=l[m],v=h.route.id,w=n[v],S=h.params,b=t[v],R=[],L={id:v,data:w,meta:[],params:h.params,pathname:h.pathname,handle:h.route.handle,error:s};if(d[m]=L,b!=null&&b.meta?R=typeof b.meta=="function"?b.meta({data:w,params:S,location:o,matches:d,error:s}):Array.isArray(b.meta)?[...b.meta]:b.meta:u&&(R=[...u]),R=R||[],!Array.isArray(R))throw new Error("The route at "+h.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);L.meta=R,d[m]=L,i=[...R],u=i}return c.createElement(c.Fragment,null,i.flat().map(m=>{if(!m)return null;if("tagName"in m){let{tagName:h,...v}=m;if(!Xi(h))return console.warn(`A meta object uses an invalid tagName: ${h}. Expected either 'link' or 'meta'`),null;let w=h;return c.createElement(w,{key:JSON.stringify(v),...v})}if("title"in m)return c.createElement("title",{key:"title"},String(m.title));if("charset"in m&&(m.charSet??(m.charSet=m.charset),delete m.charset),"charSet"in m&&m.charSet!=null)return typeof m.charSet=="string"?c.createElement("meta",{key:"charSet",charSet:m.charSet}):null;if("script:ld+json"in m)try{let h=JSON.stringify(m["script:ld+json"]);return c.createElement("script",{key:`script:ld+json:${h}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:h}})}catch{return null}return c.createElement("meta",{key:JSON.stringify(m),...m})}))}function Xi(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}var Mt=!1;function qi(e){let{manifest:t,serverHandoffString:r,isSpaMode:a,renderMeta:n,routeDiscovery:o,ssr:l}=qe(),{router:s,static:i,staticContext:u}=Sr(),{matches:d}=$t(),m=Rr(o,l);n&&(n.didRenderScripts=!0);let h=xr(d,null,a);c.useEffect(()=>{Mt=!0},[]);let v=c.useMemo(()=>{var _;let R=u?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",L=i?`${(_=t.hmr)!=null&&_.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${m?"":`import ${JSON.stringify(t.url)}`};
${h.map((D,C)=>{let p=`route${C}`,k=t.routes[D.route.id];ye(k,`Route ${D.route.id} not found in manifest`);let{clientActionModule:j,clientLoaderModule:A,clientMiddlewareModule:q,hydrateFallbackModule:ae,module:fe}=k,V=[...j?[{module:j,varName:`${p}_clientAction`}]:[],...A?[{module:A,varName:`${p}_clientLoader`}]:[],...q?[{module:q,varName:`${p}_clientMiddleware`}]:[],...ae?[{module:ae,varName:`${p}_HydrateFallback`}]:[],{module:fe,varName:`${p}_main`}];if(V.length===1)return`import * as ${p} from ${JSON.stringify(fe)};`;let G=V.map(Y=>`import * as ${Y.varName} from "${Y.module}";`).join(`
`),le=`const ${p} = {${V.map(Y=>`...${Y.varName}`).join(",")}};`;return[G,le].join(`
`)}).join(`
`)}
  ${m?`window.__reactRouterManifest = ${JSON.stringify(Bi(t,s),null,2)};`:""}
  window.__reactRouterRouteModules = {${h.map((D,C)=>`${JSON.stringify(D.route.id)}:route${C}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return c.createElement(c.Fragment,null,c.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(R),type:void 0}),c.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(L),type:"module",async:!0}))},[]),w=Mt?[]:Qi(t.entry.imports.concat(Er(h,t,{includeHydrateFallback:!0}))),S=typeof t.sri=="object"?t.sri:{};return Mt?null:c.createElement(c.Fragment,null,typeof t.sri=="object"?c.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:S})}}):null,m?null:c.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:S[t.url],suppressHydrationWarning:!0}),c.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:S[t.entry.module],suppressHydrationWarning:!0}),w.map(b=>c.createElement("link",{key:b,rel:"modulepreload",href:b,crossOrigin:e.crossOrigin,integrity:S[b],suppressHydrationWarning:!0})),v)}function Qi(e){return[...new Set(e)]}function Zi(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var ea=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{ea&&(window.__reactRouterVersion="7.6.0")}catch{}var ta=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ra=c.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:o,replace:l,state:s,target:i,to:u,preventScrollReset:d,viewTransition:m,...h},v){let{basename:w}=c.useContext(xe),S=typeof u=="string"&&ta.test(u),b,R=!1;if(typeof u=="string"&&S&&(b=u,ea))try{let A=new URL(window.location.href),q=u.startsWith("//")?new URL(A.protocol+u):new URL(u),ae=Ee(q.pathname,w);q.origin===A.origin&&ae!=null?u=ae+q.search+q.hash:R=!0}catch{ie(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let L=So(u,{relative:n}),[_,D,C]=Ji(a,h),p=rl(u,{replace:l,state:s,target:i,preventScrollReset:d,relative:n,viewTransition:m});function k(A){t&&t(A),A.defaultPrevented||p(A)}let j=c.createElement("a",{...h,...C,href:b||L,onClick:R||o?t:k,ref:Zi(v,D),target:i,"data-discover":!S&&r==="render"?"true":void 0});return _&&!S?c.createElement(c.Fragment,null,j,c.createElement(Zn,{page:L})):j});ra.displayName="Link";var el=c.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:o,to:l,viewTransition:s,children:i,...u},d){let m=ft(l,{relative:u.relative}),h=Ce(),v=c.useContext(Ge),{navigator:w,basename:S}=c.useContext(xe),b=v!=null&&sl(m)&&s===!0,R=w.encodeLocation?w.encodeLocation(m).pathname:m.pathname,L=h.pathname,_=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;r||(L=L.toLowerCase(),_=_?_.toLowerCase():null,R=R.toLowerCase()),_&&S&&(_=Ee(_,S)||_);const D=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let C=L===R||!n&&L.startsWith(R)&&L.charAt(D)==="/",p=_!=null&&(_===R||!n&&_.startsWith(R)&&_.charAt(R.length)==="/"),k={isActive:C,isPending:p,isTransitioning:b},j=C?t:void 0,A;typeof a=="function"?A=a(k):A=[a,C?"active":null,p?"pending":null,b?"transitioning":null].filter(Boolean).join(" ");let q=typeof o=="function"?o(k):o;return c.createElement(ra,{...u,"aria-current":j,className:A,ref:d,style:q,to:l,viewTransition:s},typeof i=="function"?i(k):i)});el.displayName="NavLink";var na=c.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:l=Lt,action:s,onSubmit:i,relative:u,preventScrollReset:d,viewTransition:m,...h},v)=>{let w=oa(),S=ol(s,{relative:u}),b=l.toLowerCase()==="get"?"get":"post",R=typeof s=="string"&&ta.test(s),L=_=>{if(i&&i(_),_.defaultPrevented)return;_.preventDefault();let D=_.nativeEvent.submitter,C=(D==null?void 0:D.getAttribute("formmethod"))||l;w(D||_.currentTarget,{fetcherKey:t,method:C,navigate:r,replace:n,state:o,relative:u,preventScrollReset:d,viewTransition:m})};return c.createElement("form",{ref:v,method:b,action:S,onSubmit:a?i:L,...h,"data-discover":!R&&e==="render"?"true":void 0})});na.displayName="Form";function tl({getKey:e,storageKey:t,...r}){let a=c.useContext(At),{basename:n}=c.useContext(xe),o=Ce(),l=Fn();il({getKey:e,storageKey:t});let s=c.useMemo(()=>{if(!a||!e)return null;let u=ur(o,l,n,e);return u!==o.key?u:null},[]);if(!a||a.isSpaMode)return null;let i=((u,d)=>{if(!window.history.state||!window.history.state.key){let m=Math.random().toString(32).slice(2);window.history.replaceState({key:m},"")}try{let h=JSON.parse(sessionStorage.getItem(u)||"{}")[d||window.history.state.key];typeof h=="number"&&window.scrollTo(0,h)}catch(m){console.error(m),sessionStorage.removeItem(u)}}).toString();return c.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${i})(${JSON.stringify(t||sr)}, ${JSON.stringify(s)})`}})}tl.displayName="ScrollRestoration";function aa(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Nt(e){let t=c.useContext(Ve);return H(t,aa(e)),t}function Lr(e){let t=c.useContext(Ge);return H(t,aa(e)),t}function rl(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:l}={}){let s=kn(),i=Ce(),u=ft(e,{relative:o});return c.useCallback(d=>{if(Ko(d,t)){d.preventDefault();let m=r!==void 0?r:Fe(i)===Fe(u);s(e,{replace:m,state:a,preventScrollReset:n,relative:o,viewTransition:l})}},[i,s,u,r,a,t,e,n,o,l])}function Dl(e){ie(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=c.useRef(ar(e)),r=c.useRef(!1),a=Ce(),n=c.useMemo(()=>Go(a.search,r.current?null:t.current),[a.search]),o=kn(),l=c.useCallback((s,i)=>{const u=ar(typeof s=="function"?s(n):s);r.current=!0,o("?"+u,i)},[o,n]);return[n,l]}var nl=0,al=()=>`__${String(++nl)}__`;function oa(){let{router:e}=Nt("useSubmit"),{basename:t}=c.useContext(xe),r=Fo();return c.useCallback(async(a,n={})=>{let{action:o,method:l,encType:s,formData:i,body:u}=Qo(a,t);if(n.navigate===!1){let d=n.fetcherKey||al();await e.fetch(d,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function ol(e,{relative:t}={}){let{basename:r}=c.useContext(xe),a=c.useContext(Le);H(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={...ft(e||".",{relative:t})},l=Ce();if(e==null){o.search=l.search;let s=new URLSearchParams(o.search),i=s.getAll("index");if(i.some(d=>d==="")){s.delete("index"),i.filter(m=>m).forEach(m=>s.append("index",m));let d=s.toString();o.search=d?`?${d}`:""}}return(!e||e===".")&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Me([r,o.pathname])),Fe(o)}function _l({key:e}={}){var b;let{router:t}=Nt("useFetcher"),r=Lr("useFetcher"),a=c.useContext(yr),n=c.useContext(Le),o=(b=n.matches[n.matches.length-1])==null?void 0:b.route.id;H(a,"useFetcher must be used inside a FetchersContext"),H(n,"useFetcher must be used inside a RouteContext"),H(o!=null,'useFetcher can only be used on routes that contain a unique "id"');let l=c.useId(),[s,i]=c.useState(e||l);e&&e!==s&&i(e),c.useEffect(()=>(t.getFetcher(s),()=>t.deleteFetcher(s)),[t,s]);let u=c.useCallback(async(R,L)=>{H(o,"No routeId available for fetcher.load()"),await t.fetch(s,o,R,L)},[s,o,t]),d=oa(),m=c.useCallback(async(R,L)=>{await d(R,{...L,navigate:!1,fetcherKey:s})},[s,d]),h=c.useMemo(()=>{let R=c.forwardRef((L,_)=>c.createElement(na,{...L,navigate:!1,fetcherKey:s,ref:_}));return R.displayName="fetcher.Form",R},[s]),v=r.fetchers.get(s)||bn,w=a.get(s);return c.useMemo(()=>({Form:h,submit:m,load:u,...v,data:w}),[h,m,u,v,w])}function Tl(){let e=Lr("useFetchers");return Array.from(e.fetchers.entries()).map(([t,r])=>({...r,key:t}))}var sr="react-router-scroll-positions",St={};function ur(e,t,r,a){let n=null;return a&&(r!=="/"?n=a({...e,pathname:Ee(e.pathname,r)||e.pathname},t):n=a(e,t)),n==null&&(n=e.key),n}function il({getKey:e,storageKey:t}={}){let{router:r}=Nt("useScrollRestoration"),{restoreScrollPosition:a,preventScrollReset:n}=Lr("useScrollRestoration"),{basename:o}=c.useContext(xe),l=Ce(),s=Fn(),i=Io();c.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),ll(c.useCallback(()=>{if(i.state==="idle"){let u=ur(l,s,o,e);St[u]=window.scrollY}try{sessionStorage.setItem(t||sr,JSON.stringify(St))}catch(u){ie(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${u}).`)}window.history.scrollRestoration="auto"},[i.state,e,o,l,s,t])),typeof document<"u"&&(c.useLayoutEffect(()=>{try{let u=sessionStorage.getItem(t||sr);u&&(St=JSON.parse(u))}catch{}},[t]),c.useLayoutEffect(()=>{let u=r==null?void 0:r.enableScrollRestoration(St,()=>window.scrollY,e?(d,m)=>ur(d,m,o,e):void 0);return()=>u&&u()},[r,o,e]),c.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(l.hash){let u=document.getElementById(decodeURIComponent(l.hash.slice(1)));if(u){u.scrollIntoView();return}}n!==!0&&window.scrollTo(0,0)}},[l,a,n]))}function ll(e,t){let{capture:r}={};c.useEffect(()=>{let a=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,a),()=>{window.removeEventListener("pagehide",e,a)}},[e,r])}function sl(e,t={}){let r=c.useContext(pr);H(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Nt("useViewTransitionState"),n=ft(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=Ee(r.currentLocation.pathname,a)||r.currentLocation.pathname,l=Ee(r.nextLocation.pathname,a)||r.nextLocation.pathname;return _t(n.pathname,l)!=null||_t(n.pathname,o)!=null}[...Yn];function kl(e){if(!e)return null;let t=Object.entries(e),r={};for(let[a,n]of t)if(n&&n.__type==="RouteErrorResponse")r[a]=new Ie(n.status,n.statusText,n.data,n.internal===!0);else if(n&&n.__type==="Error"){if(n.__subType){let o=window[n.__subType];if(typeof o=="function")try{let l=new o(n.message);l.stack=n.stack,r[a]=l}catch{}}if(r[a]==null){let o=new Error(n.message);o.stack=n.stack,r[a]=o}}else r[a]=n;return r}function Ol(e,t,r,a,n,o){let l={...e,loaderData:{...e.loaderData}},s=Pe(t,a,n);if(s)for(let i of s){let u=i.route.id,d=r(u);qn(u,d.clientLoader,d.hasLoader,o)&&(d.hasHydrateFallback||!d.hasLoader)?delete l.loaderData[u]:d.hasLoader||(l.loaderData[u]=null)}return l}export{bl as A,vl as B,gl as C,Ra as D,Tl as E,na as F,$o as G,pl as H,Pl as I,qi as J,Be as K,ra as L,Ml as M,el as N,El as O,ul as R,tl as S,Io as a,Dl as b,kn as c,_l as d,yl as e,Fn as f,hl as g,In as h,Ce as i,fl as j,H as k,Cl as l,At as m,Sl as n,wl as o,$i as p,br as q,c as r,Ol as s,kl as t,ml as u,dl as v,Ll as w,Rl as x,cl as y,xl as z};
