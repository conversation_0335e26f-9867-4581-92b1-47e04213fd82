import { useState, useEffect } from 'react';
import { UTMLinks } from '~/components/UTMLink';

interface PromoPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PromoPopup({ isOpen, onClose }: PromoPopupProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set client flag to prevent hydration mismatch
    setIsClient(true);

    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !isClient) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Popup Container */}
      <div className="relative z-10 max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-20 w-10 h-10 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
          aria-label="Close popup"
        >
          <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Popup Content */}
        <div className="relative bg-white rounded-lg shadow-2xl overflow-hidden border-4 border-white">
          {/* Desktop Image */}
          {!isMobile && (
            <div className="relative">
              <img
                src="/desktoppopup.webp"
                alt="Special Offer"
                className="w-full h-auto max-h-[80vh] object-contain"
              />
              {/* Desktop Shop Coffee Button - Bottom Left */}
              <div className="absolute bottom-6 left-6">
                <UTMLinks.PopupCTA
                  className="inline-flex items-center px-8 py-4 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
                >
                  <span className="text-white">Shop Coffee</span>
                  <svg className="w-5 h-5 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </UTMLinks.PopupCTA>
              </div>
            </div>
          )}

          {/* Mobile Image */}
          {isMobile && (
            <div className="relative">
              <img
                src="/mobilepopup.webp"
                alt="Special Offer"
                className="w-full h-auto max-h-[80vh] object-contain"
              />
              {/* Mobile Shop Coffee Button - Bottom Center */}
              <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
                <UTMLinks.PopupCTA
                  className="inline-flex items-center px-6 py-3 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-sm"
                >
                  <span className="text-white">Shop Coffee</span>
                  <svg className="w-4 h-4 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </UTMLinks.PopupCTA>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
