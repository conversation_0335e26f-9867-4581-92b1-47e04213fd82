/* Figma Selection: iPhone 16 Pro - 1 (https://www.figma.com/design/WYbvj4CpZ4WbggqZFfWg5y/big-river?node-id=112-7) */

#node-112_7 {
  position: relative;
  width: 430px;
  height: 932px;
  background: #fff;
}

#node-113_8 {
  position: absolute;
  left: 34px;
  top: 196px;
  width: 334px;
  height: 152px;
  background: url('http://localhost:3845/assets/fbf34aa9c01dc781dbcdd0841615b6a58c35bc5c.png') center/cover no-repeat;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.25);
  border: 1px solid #000;
}
#node-113_10, #node-113_14, #node-113_17 {
  position: absolute;
  left: 34px;
  width: 334px;
  height: 56px;
}
#node-113_10 { top: 358px; }
#node-113_14 { top: 424px; }
#node-113_17 { top: 490px; }

#node-113_12, #node-113_15, #node-113_18 {
  position: absolute;
  left: 40px;
  width: 323px;
  height: 43px;
  background: #eeedc1;
}
#node-113_12 { top: 365px; }
#node-113_15 { top: 431px; }
#node-113_18 { top: 497px; }

#node-113_20 {
  position: absolute;
  left: 34px;
  top: 554px;
  width: 334px;
  height: 55.054px;
  display: contents;
}
#node-113_21 {
  position: absolute;
  left: 34px;
  top: 554px;
  width: 334px;
  height: 55.054px;
  background: #282424;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
#node-113_22 {
  position: absolute;
  left: 38.831px;
  top: 560.477px;
  width: 325.948px;
  height: 42.1px;
  background: #f2dd00;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
#node-113_24 {
  position: absolute;
  left: 87px;
  top: 558px;
  width: 228.438px;
  height: 44.799px;
  display: contents;
}
#node-113_25 {
  position: absolute;
  left: 87px;
  top: 558px;
  width: 228.438px;
  height: 44.799px;
}
#node-113_25 img {
  display: block;
  max-width: none;
  width: 100%;
  height: 100%;
}

#node-113_27, #node-113_29, #node-113_31 {
  position: absolute;
  left: 50%;
  width: 343px;
  height: 99px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #000;
  text-align: center;
  line-height: 1em;
  transform: translate(-50%, -50%);
}
#node-113_27 { top: 387.5px; }
#node-113_29 { top: 452.5px; width: 323px; left: 50%; }
#node-113_31 { top: 520.5px; width: 332px; left: 50%; } 