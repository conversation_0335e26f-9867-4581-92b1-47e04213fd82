import {Form, useActionData, useNavigation, Link, type MetaFunction} from 'react-router';
import {type ActionFunctionArgs} from '@shopify/remix-oxygen';
import {useState, useEffect} from 'react';
import {initScrollAnimations} from '~/lib/scrollAnimations';

// Type definitions for better type safety
interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

interface ActionData {
  success?: boolean;
  errors?: Record<string, string>;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export const meta: MetaFunction = () => {
  return [
    { title: 'Contact Us | Big River Coffee' },
    { description: 'At Big River Coffee, we love hearing from our customers and value your feedback. Contact us for questions, comments, or assistance.' }
  ];
};

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  const admin = context.admin as any;
  // Parse form data
  const formData = await request.formData();
  const firstName = formData.get('first-name') as string;
  const lastName = formData.get('last-name') as string;
  const email = formData.get('email') as string;
  const phone = formData.get('phone') as string || '';
  const subject = formData.get('subject') as string;
  const message = formData.get('message') as string;

  // Enhanced validation with email format check
  const errors: Record<string, string> = {};
  if (!firstName?.trim()) errors['first-name'] = 'First name is required';
  if (!lastName?.trim()) errors['last-name'] = 'Last name is required';
  if (!email?.trim()) {
    errors['email'] = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors['email'] = 'Please enter a valid email address';
  }
  if (!subject?.trim()) errors['subject'] = 'Subject is required';
  if (!message?.trim()) errors['message'] = 'Message is required';

  if (Object.keys(errors).length) {
    return json({success: false, errors}, {status: 400});
  }

  try {
    // Create metaobject entry using Admin API
    const data = await admin.request(`
      mutation CreateContactFormSubmission($input: MetaobjectCreateInput!) {
        metaobjectCreate(metaobject: $input) {
          metaobject {
            id
            handle
          }
          userErrors {
            field
            message
            code
          }
        }
      }
    `, {
      variables: {
        input: {
          type: "contact_form",
          fields: [
            { key: "first_name", value: firstName.trim() },
            { key: "last_name", value: lastName.trim() },
            { key: "email", value: email.trim().toLowerCase() },
            { key: "phone", value: phone.trim() },
            { key: "subject", value: subject.trim() },
            { key: "message", value: message.trim() },
            { key: "submitted_at", value: new Date().toISOString() }
          ]
        }
      }
    });

    console.log('Admin API response:', data);

    // Check for user errors from the mutation
    if (data.data?.metaobjectCreate?.userErrors?.length > 0) {
      const userErrors = data.data.metaobjectCreate.userErrors;
      console.error('User errors:', userErrors);
      console.error('Detailed user errors:', JSON.stringify(userErrors, null, 2));

      // Map user errors to form field errors if possible
      const fieldErrors: Record<string, string> = {};
      userErrors.forEach((error: any) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return json({
        success: false,
        errors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        error: Object.keys(fieldErrors).length === 0 ? 'Failed to submit form. Please check your information and try again.' : undefined
      }, { status: 400 });
    }

    // Success case
    if (data.data?.metaobjectCreate?.metaobject?.id) {
      console.log('Contact form submitted successfully:', data.data.metaobjectCreate.metaobject.id);
      return json({ success: true });
    }

    // Fallback error case
    return json({
      success: false,
      error: 'Unexpected response from server. Please try again.'
    }, { status: 500 });

  } catch (error) {
    console.error('Error creating contact form submission:', error);
    return json({
      success: false,
      error: 'Unable to submit form at this time. Please try again later or contact us directly.'
    }, { status: 500 });
  }
}

export default function ContactPage() {
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Update form submitted state when action completes successfully
  useEffect(() => {
    if (actionData?.success && !formSubmitted) {
      setFormSubmitted(true);
    }
  }, [actionData?.success, formSubmitted]);

  // Add body class for borderless styling and initialize animations
  useEffect(() => {
    if (typeof window === 'undefined') return;

    document.body.classList.add('contact');
    initScrollAnimations();

    return () => {
      document.body.classList.remove('contact');
    };
  }, []);

  // Show success message if form was submitted successfully
  if (actionData?.success || formSubmitted) {
    return (
      <div className="min-h-screen relative overflow-hidden" style={{ backgroundColor: '#f97316' }}>
        {/* Background decorative circles */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
        <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
        <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
        <div className="absolute bottom-1/4 left-1/3 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>

        <div className="max-w-4xl mx-auto px-6 py-20 relative z-10">
          <div className="text-center">
            <div className="bg-white rounded-2xl p-12 shadow-2xl border-4 border-army-600 animate-scale-in">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-5xl font-bold text-army-600 mb-4" style={{ fontFamily: 'var(--font-title)' }}>
                Thank You!
              </h1>
              <div className="w-24 h-1 bg-army-600 rounded mb-6 mx-auto"></div>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                Your message has been submitted successfully. We'll get back to you as soon as possible.
              </p>
              <Link
                to="/"
                className="inline-flex items-center px-8 py-4 bg-army-600 text-white rounded-xl font-semibold text-lg hover:bg-army-700 hover:scale-105 transition-all duration-300 shadow-lg"
                style={{ fontFamily: 'var(--font-header)' }}
              >
                Return to Homepage
                <svg className="ml-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
    <style>{`
      body.contact {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.contact main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

    <div className="min-h-screen relative overflow-hidden" style={{
      backgroundColor: '#eeedc1',
      margin: 0,
      padding: 0,
      width: '100vw',
      minHeight: '100vh'
    }}>
      {/* Enhanced Background decorative circles */}
      <div className="absolute top-16 left-8 w-36 h-36 bg-army-600 rounded-full opacity-15"></div>
      <div className="absolute bottom-32 right-12 w-28 h-28 bg-army-600 rounded-full opacity-25"></div>
      <div className="absolute top-1/4 left-1/3 w-20 h-20 bg-army-600 rounded-full opacity-18"></div>
      <div className="absolute top-1/2 right-1/5 w-24 h-24 bg-army-600 rounded-full opacity-22"></div>
      <div className="absolute bottom-1/3 left-1/5 w-16 h-16 bg-army-600 rounded-full opacity-20"></div>
      <div className="absolute top-3/4 right-1/3 w-32 h-32 bg-army-600 rounded-full opacity-12"></div>
      <div className="absolute top-8 right-8 w-12 h-12 bg-army-600 rounded-full opacity-30"></div>
      <div className="absolute bottom-8 left-1/2 w-14 h-14 bg-army-600 rounded-full opacity-16"></div>

      <div className="w-full px-6 py-20 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16 scroll-fade-in">
            <div className="bg-gradient-to-r from-army-600 to-army-700 rounded-3xl p-12 mb-12 shadow-2xl border-4 border-white">
              <span className="text-army-600 font-bold text-sm uppercase tracking-wider bg-white px-6 py-2 rounded-full shadow-lg inline-block mb-6" style={{ fontFamily: 'var(--font-header)' }}>Get In Touch</span>
              <h1 className="text-6xl lg:text-7xl font-bold text-white mb-6" style={{ fontFamily: 'var(--font-title)' }}>
                Contact Us
              </h1>
              <div className="w-32 h-2 bg-white rounded-full mb-8 mx-auto"></div>
              <div className="flex justify-center">
                <div className="text-center max-w-3xl">
                  <p className="text-white text-xl leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                    We'd love to hear from you! Whether you have a question about our products,
                    need help with an order, or just want to say hello, please fill out the form below
                    and we'll get back to you as soon as possible.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Info Section */}
          <div className="mb-16 text-center scroll-fade-in delay-100">
            <div className="bg-gradient-to-r from-army-600 to-army-700 rounded-2xl overflow-hidden py-12 shadow-2xl border-4 border-white">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12 w-full px-8">
                <div className="flex flex-col items-center group">
                  <div className="w-20 h-20 rounded-full bg-white flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3" style={{ fontFamily: 'var(--font-header)' }}>Email</h3>
                  <p className="text-white text-lg" style={{ fontFamily: 'var(--font-body)' }}><EMAIL></p>
                </div>

                <div className="flex flex-col items-center group">
                  <div className="w-20 h-20 rounded-full bg-white flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3" style={{ fontFamily: 'var(--font-header)' }}>Questions?</h3>
                  <p className="text-white text-lg" style={{ fontFamily: 'var(--font-body)' }}>We're here to help with any inquiries</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form Section */}
          <div className="bg-white rounded-2xl overflow-hidden shadow-2xl border-4 border-army-600 scroll-fade-in delay-200">
            <div className="p-12 md:p-16">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-army-600 mb-4" style={{ fontFamily: 'var(--font-title)' }}>
                  Send Us a Message
                </h2>
                <div className="w-24 h-1 bg-army-600 rounded mb-6 mx-auto"></div>
                <p className="text-gray-600 text-lg" style={{ fontFamily: 'var(--font-body)' }}>
                  Fill out the form below and we'll get back to you soon
                </p>
              </div>

              <Form method="post" className="space-y-8 max-w-4xl mx-auto">
                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                  <div className="group">
                    <label htmlFor="first-name" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                      First name
                    </label>
                    <input
                      type="text"
                      name="first-name"
                      id="first-name"
                      autoComplete="given-name"
                      className={`block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg transition-all duration-300 group-hover:border-army-400 ${
                        actionData?.errors?.['first-name'] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      }`}
                      style={{ fontFamily: 'var(--font-body)' }}
                      placeholder="Your first name"
                      required
                    />
                    {actionData?.errors?.['first-name'] && (
                      <p className="text-red-500 text-sm mt-2 font-medium">{actionData.errors['first-name']}</p>
                    )}
                  </div>

                  <div className="group">
                    <label htmlFor="last-name" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                      Last name
                    </label>
                    <input
                      type="text"
                      name="last-name"
                      id="last-name"
                      autoComplete="family-name"
                      className={`block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg transition-all duration-300 group-hover:border-army-400 ${
                        actionData?.errors?.['last-name'] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      }`}
                      style={{ fontFamily: 'var(--font-body)' }}
                      placeholder="Your last name"
                      required
                    />
                    {actionData?.errors?.['last-name'] && (
                      <p className="text-red-500 text-sm mt-2 font-medium">{actionData.errors['last-name']}</p>
                    )}
                  </div>
                </div>

                <div className="group">
                  <label htmlFor="email" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    autoComplete="email"
                    className={`block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg transition-all duration-300 group-hover:border-army-400 ${
                      actionData?.errors?.['email'] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                    style={{ fontFamily: 'var(--font-body)' }}
                    placeholder="<EMAIL>"
                    required
                  />
                  {actionData?.errors?.['email'] && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{actionData.errors['email']}</p>
                  )}
                </div>

                <div className="group">
                  <label htmlFor="phone" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                    Phone (optional)
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    id="phone"
                    autoComplete="tel"
                    className="block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg transition-all duration-300 group-hover:border-army-400"
                    style={{ fontFamily: 'var(--font-body)' }}
                    placeholder="(*************"
                  />
                </div>

                <div className="group">
                  <label htmlFor="subject" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    className="block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg appearance-none transition-all duration-300 group-hover:border-army-400"
                    style={{ fontFamily: 'var(--font-body)' }}
                    defaultValue="Order Inquiry"
                  >
                    <option>Order Inquiry</option>
                    <option>Product Question</option>
                    <option>Brewing Help</option>
                    <option>Feedback</option>
                    <option>Other</option>
                  </select>
                </div>

                <div className="group">
                  <label htmlFor="message" className="block text-sm font-bold text-army-600 text-left mb-3" style={{ fontFamily: 'var(--font-header)' }}>
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={6}
                    className={`block w-full rounded-xl border-2 border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-4 px-6 text-lg transition-all duration-300 group-hover:border-army-400 resize-none ${
                      actionData?.errors?.['message'] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                    style={{ fontFamily: 'var(--font-body)' }}
                    placeholder="Your message here..."
                    required
                  ></textarea>
                  {actionData?.errors?.['message'] && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{actionData.errors['message']}</p>
                  )}
                </div>

                <div className="pt-8">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex justify-center items-center py-5 px-8 border border-transparent rounded-xl shadow-lg text-xl font-bold text-white bg-gradient-to-r from-army-600 to-army-700 hover:from-army-700 hover:to-army-800 hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                    style={{ fontFamily: 'var(--font-header)' }}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        Send Message
                        <svg className="ml-3 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </>
                    )}
                  </button>
                </div>

                {actionData?.error && (
                  <div className="bg-red-50 border-2 border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span className="font-medium">{actionData.error}</span>
                    </div>
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
