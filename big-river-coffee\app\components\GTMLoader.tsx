import { useEffect } from 'react';

declare global {
  interface Window {
    dataLayer: any[];
  }
}

/**
 * Client-side Google Tag Manager loader
 * Loads GTM after React hydration to prevent hydration mismatches
 */
export function GTMLoader() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Check if GTM is already loaded
    if (window.dataLayer) return;

    // Initialize dataLayer
    window.dataLayer = window.dataLayer || [];

    // Add GTM initialization
    window.dataLayer.push({
      'gtm.start': new Date().getTime(),
      event: 'gtm.js'
    });

    // Load GTM script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://www.googletagmanager.com/gtm.js?id=GTM-WXN2JD85';

    script.onload = () => {
      // GTM loaded successfully
      console.log('Google Tag Manager loaded');
    };

    script.onerror = () => {
      console.error('Failed to load Google Tag Manager');
    };

    // Append script to head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      // Remove script if component unmounts (though this rarely happens)
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  return null; // This component doesn't render anything
}
