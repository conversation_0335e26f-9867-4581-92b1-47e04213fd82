/**
 * Performance optimization utilities for Big River Coffee
 * Implements aggressive caching, prefetching, and scalability strategies
 */

// Cache configuration for different types of data
export const CACHE_STRATEGIES = {
  // Product data - cache for 1 hour, stale-while-revalidate for 24 hours
  PRODUCT: {
    maxAge: 3600,
    staleWhileRevalidate: 86400,
    headers: {
      'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
      'CDN-Cache-Control': 'public, max-age=3600',
      'Vercel-CDN-Cache-Control': 'public, max-age=3600',
    },
  },
  
  // Collection data - cache for 30 minutes, stale-while-revalidate for 12 hours
  COLLECTION: {
    maxAge: 1800,
    staleWhileRevalidate: 43200,
    headers: {
      'Cache-Control': 'public, max-age=1800, stale-while-revalidate=43200',
      'CDN-Cache-Control': 'public, max-age=1800',
      'Vercel-CDN-Cache-Control': 'public, max-age=1800',
    },
  },
  
  // Static assets - cache for 1 year
  STATIC: {
    maxAge: 31536000,
    staleWhileRevalidate: 31536000,
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable',
      'CDN-Cache-Control': 'public, max-age=31536000',
      'Vercel-CDN-Cache-Control': 'public, max-age=31536000',
    },
  },
  
  // API responses - cache for 5 minutes, stale-while-revalidate for 1 hour
  API: {
    maxAge: 300,
    staleWhileRevalidate: 3600,
    headers: {
      'Cache-Control': 'public, max-age=300, stale-while-revalidate=3600',
      'CDN-Cache-Control': 'public, max-age=300',
      'Vercel-CDN-Cache-Control': 'public, max-age=300',
    },
  },
} as const;

/**
 * Prefetch critical resources for faster navigation
 */
export class ResourcePrefetcher {
  private prefetchedUrls = new Set<string>();
  private prefetchQueue: string[] = [];
  private isProcessing = false;

  constructor() {
    // Start processing queue when browser is idle
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      this.scheduleProcessing();
    }
  }

  /**
   * Prefetch a URL with priority
   */
  prefetch(url: string, priority: 'high' | 'low' = 'low') {
    if (this.prefetchedUrls.has(url)) return;
    
    if (priority === 'high') {
      this.prefetchQueue.unshift(url);
    } else {
      this.prefetchQueue.push(url);
    }
    
    this.scheduleProcessing();
  }

  /**
   * Prefetch product pages that are likely to be visited
   */
  prefetchProductPages(productHandles: string[]) {
    productHandles.forEach(handle => {
      this.prefetch(`/products/${handle}`, 'low');
    });
  }

  /**
   * Prefetch collection pages
   */
  prefetchCollectionPages(collectionHandles: string[]) {
    collectionHandles.forEach(handle => {
      this.prefetch(`/collections/${handle}`, 'low');
    });
  }

  private scheduleProcessing() {
    if (this.isProcessing || this.prefetchQueue.length === 0) return;
    
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => this.processQueue(), { timeout: 5000 });
    } else {
      setTimeout(() => this.processQueue(), 100);
    }
  }

  private processQueue() {
    this.isProcessing = true;
    
    while (this.prefetchQueue.length > 0) {
      const url = this.prefetchQueue.shift()!;
      
      if (!this.prefetchedUrls.has(url)) {
        this.createPrefetchLink(url);
        this.prefetchedUrls.add(url);
      }
      
      // Process one at a time to avoid overwhelming the browser
      break;
    }
    
    this.isProcessing = false;
    
    // Schedule next processing if queue is not empty
    if (this.prefetchQueue.length > 0) {
      setTimeout(() => this.scheduleProcessing(), 50);
    }
  }

  private createPrefetchLink(url: string) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    document.head.appendChild(link);
  }
}

/**
 * Image optimization utilities
 */
export class ImageOptimizer {
  /**
   * Generate optimized image URL with Shopify transformations
   */
  static optimizeImageUrl(
    originalUrl: string,
    options: {
      width?: number;
      height?: number;
      format?: 'webp' | 'jpg' | 'png';
      quality?: number;
    } = {}
  ): string {
    if (!originalUrl) return '';
    
    const url = new URL(originalUrl);
    const params = new URLSearchParams(url.search);
    
    if (options.width) params.set('width', options.width.toString());
    if (options.height) params.set('height', options.height.toString());
    if (options.format) params.set('format', options.format);
    if (options.quality) params.set('quality', options.quality.toString());
    
    url.search = params.toString();
    return url.toString();
  }

  /**
   * Generate responsive image srcSet
   */
  static generateSrcSet(
    originalUrl: string,
    widths: number[] = [320, 640, 768, 1024, 1280],
    format?: 'webp' | 'jpg' | 'png'
  ): string {
    return widths
      .map(width => {
        const optimizedUrl = this.optimizeImageUrl(originalUrl, { width, format });
        return `${optimizedUrl} ${width}w`;
      })
      .join(', ');
  }

  /**
   * Preload critical images
   */
  static preloadImage(url: string, priority: 'high' | 'low' = 'high') {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    if (priority === 'high') {
      link.setAttribute('fetchpriority', 'high');
    }
    document.head.appendChild(link);
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static metrics: Record<string, number> = {};

  /**
   * Mark the start of a performance measurement
   */
  static mark(name: string) {
    if (typeof performance !== 'undefined') {
      performance.mark(`${name}-start`);
    }
  }

  /**
   * Measure the time since a mark was set
   */
  static measure(name: string): number {
    if (typeof performance === 'undefined') return 0;
    
    try {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      const measure = performance.getEntriesByName(name)[0];
      const duration = measure?.duration || 0;
      
      this.metrics[name] = duration;
      return duration;
    } catch (error) {
      console.warn(`Failed to measure ${name}:`, error);
      return 0;
    }
  }

  /**
   * Get all collected metrics
   */
  static getMetrics(): Record<string, number> {
    return { ...this.metrics };
  }

  /**
   * Log Core Web Vitals
   */
  static logWebVitals() {
    if (typeof window === 'undefined') return;

    // Log when page is fully loaded
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        console.log('Performance Metrics:', {
          'Time to First Byte': navigation.responseStart - navigation.requestStart,
          'DOM Content Loaded': navigation.domContentLoadedEventEnd - navigation.startTime,
          'Load Complete': navigation.loadEventEnd - navigation.startTime,
          'Custom Metrics': this.getMetrics(),
        });
      }, 1000);
    });
  }
}

// Global instances
export const prefetcher = typeof window !== 'undefined' ? new ResourcePrefetcher() : null;

/**
 * Initialize performance optimizations
 */
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  // Start performance monitoring
  PerformanceMonitor.logWebVitals();

  // Prefetch common pages
  if (prefetcher) {
    // Prefetch collections page (most common entry point)
    prefetcher.prefetch('/collections/all', 'high');
    
    // Prefetch popular product categories
    prefetcher.prefetchCollectionPages(['coffee', 'k-cups', 'gear']);
  }

  // Optimize images on page load
  document.addEventListener('DOMContentLoaded', () => {
    // Preload hero images
    const heroImages = document.querySelectorAll('[data-hero-image]');
    heroImages.forEach((img) => {
      const src = img.getAttribute('src') || img.getAttribute('data-src');
      if (src) {
        ImageOptimizer.preloadImage(src, 'high');
      }
    });
  });
}
