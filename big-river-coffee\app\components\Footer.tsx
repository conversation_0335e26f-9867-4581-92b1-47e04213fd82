import {Suspense} from 'react';
import {Await, NavLink} from 'react-router';
import type {<PERSON>er<PERSON><PERSON><PERSON>, HeaderQuery} from 'storefrontapi.generated';
import {NewsletterSignup} from './NewsletterSignup';

interface FooterProps {
  footer: Promise<FooterQuery | null>;
  header: HeaderQuery;
  publicStoreDomain: string;
}

export function Footer({
  footer: footerPromise,
  header,
  publicStoreDomain,
}: FooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="text-white mt-auto" style={{ backgroundColor: '#3A5C5C' }}>
      {/* Newsletter Signup Banner */}
      <NewsletterSignup />

      {/* Main Footer Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo and About */}
          <div className="col-span-1 md:col-span-2">
            <NavLink to="/" className="inline-block mb-6">
              <img
                src="/Logo_Official.svg"
                alt="Big River Coffee"
                className="h-20 w-auto"
              />
            </NavLink>
            <p className="text-white mb-12 text-base leading-relaxed max-w-md">
              Premium coffee for adventurers. Ethically sourced from mountain regions,
              expertly roasted for those who live life to the fullest.
            </p>
            <div className="flex space-x-4 mt-4">
              <a
                href="https://www.instagram.com/bigriver.coffee/"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200"
                aria-label="Follow us on Instagram"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a
                href="https://www.facebook.com/bigriverco/"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200"
                aria-label="Follow us on Facebook"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a
                href="https://www.tiktok.com/@bigriver.coffee"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200"
                aria-label="Follow us on TikTok"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Shop */}
          <div>
            <h3 className="text-white text-lg font-semibold mb-6">
              Shop
            </h3>
            <ul className="space-y-3">
              <li>
                <NavLink to="/collections/all" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  Coffee
                </NavLink>
              </li>
              <li>
                <NavLink to="/collections/all" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  K-Cups
                </NavLink>
              </li>
              <li>
                <NavLink to="/collections/all" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  Subscriptions
                </NavLink>
              </li>
              <li>
                <NavLink to="/our-story" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  Our Story
                </NavLink>
              </li>
            </ul>
          </div>

          {/* Help */}
          <div>
            <h3 className="text-white text-lg font-semibold mb-6">
              Help
            </h3>
            <ul className="space-y-3">

              <li>
                <NavLink to="/policies/shipping-policy" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  Shipping Info
                </NavLink>
              </li>
              <li>
                <NavLink to="/policies/refund-policy" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  Returns
                </NavLink>
              </li>
              <li>
                <NavLink to="/account" className="text-white hover:text-orange-300 transition-colors duration-200 text-sm">
                  My Account
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-white/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="md:flex md:items-center md:justify-between">
            <div className="text-sm text-white">
              &copy; {currentYear} Big River Coffee. All rights reserved.
            </div>
            <div className="mt-4 md:mt-0">
              <Suspense>
                <Await resolve={footerPromise}>
                  {(footer) => (
                    footer?.menu && header.shop.primaryDomain?.url && (
                      <FooterMenu
                        menu={footer.menu}
                        primaryDomainUrl={header.shop.primaryDomain.url}
                        publicStoreDomain={publicStoreDomain}
                      />
                    )
                  )}
                </Await>
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

function FooterMenu({
  menu,
  primaryDomainUrl,
  publicStoreDomain,
}: {
  menu: FooterQuery['menu'];
  primaryDomainUrl: FooterProps['header']['shop']['primaryDomain']['url'];
  publicStoreDomain: string;
}) {
  return (
    <nav role="navigation">
      <ul className="flex flex-wrap gap-6">
        {(menu || FALLBACK_FOOTER_MENU).items.map((item) => {
          if (!item.url) return null;
          // if the url is internal, we strip the domain
          const url =
            item.url.includes('myshopify.com') ||
            item.url.includes(publicStoreDomain) ||
            item.url.includes(primaryDomainUrl)
              ? new URL(item.url).pathname
              : item.url;
          const isExternal = !url.startsWith('/');
          return (
            <li key={item.id}>
              {isExternal ? (
                <a
                  href={url}
                  rel="noopener noreferrer"
                  target="_blank"
                  className="text-sm text-white hover:text-orange-300 transition-colors duration-200"
                >
                  {item.title}
                </a>
              ) : (
                <NavLink
                  end
                  prefetch="intent"
                  to={url}
                  className="text-sm text-white hover:text-orange-300 transition-colors duration-200"
                >
                  {item.title}
                </NavLink>
              )}
            </li>
          );
        })}
      </ul>
    </nav>
  );
}

const FALLBACK_FOOTER_MENU = {
  id: 'gid://shopify/Menu/199655620664',
  items: [
    {
      id: 'gid://shopify/MenuItem/461633060920',
      resourceId: 'gid://shopify/ShopPolicy/23358046264',
      tags: [],
      title: 'Privacy Policy',
      type: 'SHOP_POLICY',
      url: '/policies/privacy-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633093688',
      resourceId: 'gid://shopify/ShopPolicy/23358013496',
      tags: [],
      title: 'Refund Policy',
      type: 'SHOP_POLICY',
      url: '/policies/refund-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633126456',
      resourceId: 'gid://shopify/ShopPolicy/23358111800',
      tags: [],
      title: 'Shipping Policy',
      type: 'SHOP_POLICY',
      url: '/policies/shipping-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633159224',
      resourceId: 'gid://shopify/ShopPolicy/23358079032',
      tags: [],
      title: 'Terms of Service',
      type: 'SHOP_POLICY',
      url: '/policies/terms-of-service',
      items: [],
    },
  ],
};


