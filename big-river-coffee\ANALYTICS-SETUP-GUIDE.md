# 📊 Google Analytics & GTM Setup Guide - Big River Coffee

## 🚀 Overview

✅ **ANALYTICS CONFIGURED** - Big River Coffee analytics are now set up with:

- **Google Analytics 4** (G-KWTBMRWDGP) - Active and tracking
- **Google Tag Manager** (GTM-WXN2JD85) - Configured and deployed
- **Shopify Analytics** integration with privacy compliance
- **Enhanced e-commerce events** for coffee subscription business

This guide documents the current setup and provides testing instructions.

---

## 📋 Prerequisites

Before starting, ensure you have:
- [ ] Google account with admin access
- [ ] Access to Big River Coffee's Google Analytics account (or ability to create one)
- [ ] Access to Google Tag Manager account (or ability to create one)
- [ ] Admin access to Shopify store

---

## 🔧 Step 1: Set Up Google Analytics 4

### 1.1 Create GA4 Property

1. **Go to Google Analytics** → [analytics.google.com](https://analytics.google.com)
2. **Click "Admin"** (gear icon in bottom left)
3. **Create Account** (if needed):
   - Account name: "Big River Coffee"
   - Data sharing settings: Enable recommended options
4. **Create Property**:
   - Property name: "Big River Coffee Website"
   - Reporting time zone: Your business timezone
   - Currency: USD
5. **Set up data stream**:
   - Choose "Web"
   - Website URL: `https://your-domain.com` (your production domain)
   - Stream name: "Big River Coffee Website"

### 1.2 Get Your GA4 Measurement ID

After creating the data stream:
1. **Copy the Measurement ID** (format: `G-XXXXXXXXXX`)
2. **Save this ID** - you'll need it in Step 3

### 1.3 Configure Enhanced E-commerce

1. **Go to Admin** → **Data display** → **Enhanced measurement**
2. **Enable these events**:
   - [x] Page views
   - [x] Scrolls
   - [x] Outbound clicks
   - [x] Site search
   - [x] Video engagement
   - [x] File downloads

---

## 🏷️ Step 2: Set Up Google Tag Manager

### 2.1 Create GTM Container

1. **Go to Google Tag Manager** → [tagmanager.google.com](https://tagmanager.google.com)
2. **Create Account**:
   - Account name: "Big River Coffee"
   - Country: Your business country
3. **Create Container**:
   - Container name: "Big River Coffee Website"
   - Target platform: "Web"
4. **Copy the Container ID** (format: `GTM-XXXXXXX`)

### 2.2 Configure GA4 in GTM

1. **Create GA4 Configuration Tag**:
   - Tag type: "Google Analytics: GA4 Configuration"
   - Measurement ID: Your GA4 ID from Step 1.2
   - Trigger: "All Pages"

2. **Create Enhanced E-commerce Tags**:
   - Purchase events
   - Add to cart events
   - Product view events
   - Checkout events

### 2.3 Set Up Custom Events for Coffee Business

Create these custom events in GTM:
- `subscription_started` - When customer starts a subscription
- `coffee_type_selected` - Track coffee preferences
- `brewing_guide_viewed` - Track engagement with brewing content

---

## ✅ Step 3: Code Configuration (COMPLETED)

### 3.1 Analytics IDs Configured

✅ **Google Analytics 4**: `G-KWTBMRWDGP` - Configured in `app/components/GoogleAnalytics.tsx`
✅ **Google Tag Manager**: `GTM-WXN2JD85` - Configured in `app/root.tsx`

The following files have been updated with production analytics IDs:
- `app/components/GoogleAnalytics.tsx` - GA4 tracking implementation
- `app/root.tsx` - GTM container integration

### 3.2 Environment Variables (Optional)

For better security, you can use environment variables:

1. **Add to your `.env` file**:
   ```
   PUBLIC_GA4_MEASUREMENT_ID=G-YOUR-ACTUAL-ID
   PUBLIC_GTM_CONTAINER_ID=GTM-YOUR-ACTUAL-ID
   ```

2. **Update the code** to use environment variables:
   ```typescript
   // In GoogleAnalytics.tsx
   script.src = `https://www.googletagmanager.com/gtag/js?id=${env.PUBLIC_GA4_MEASUREMENT_ID}`;
   
   // In root.tsx
   src={`https://www.googletagmanager.com/gtm.js?id=${env.PUBLIC_GTM_CONTAINER_ID}`}
   ```

---

## 🧪 Step 4: Testing

### 4.1 Local Testing

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Open browser Developer Tools** (F12)
3. **Check Console** for:
   ```
   Google Analytics loaded successfully for Big River Coffee
   ```

4. **Test GTM** in console:
   ```javascript
   console.log(window.dataLayer); // Should show events
   console.log(typeof window.gtag); // Should return "function"
   console.log('GA4 ID: G-KWTBMRWDGP'); // Verify correct ID
   console.log('GTM ID: GTM-WXN2JD85'); // Verify correct ID
   ```

### 4.2 Production Testing

1. **Deploy to production**
2. **Use Google Analytics Debugger** browser extension
3. **Check Real-time reports** in GA4
4. **Test e-commerce events**:
   - View product pages
   - Add items to cart
   - Complete a purchase

---

## 📊 Step 5: Shopify Analytics Integration

### 5.1 Enable Privacy Banner

The code already enables Shopify's privacy banner:
```typescript
withPrivacyBanner: true, // Enable Shopify's privacy banner for GDPR compliance
```

### 5.2 Configure Shopify Analytics

1. **Go to Shopify Admin** → **Analytics**
2. **Enable Enhanced E-commerce** tracking
3. **Connect Google Analytics** (if available in your Shopify plan)

---

## 🎯 Step 6: Set Up Goals & Conversions

### 6.1 GA4 Conversions

1. **Go to GA4** → **Admin** → **Conversions**
2. **Mark these events as conversions**:
   - `purchase` (auto-enabled)
   - `subscription_started` (custom)
   - `add_to_cart`
   - `begin_checkout`

### 6.2 GTM Goals

Set up goals for:
- Newsletter signups
- Subscription starts
- High-value purchases ($50+)
- Repeat customers

---

## ✅ Verification Checklist

After setup, verify:
- [ ] GA4 shows real-time data
- [ ] GTM container is firing tags
- [ ] E-commerce events are tracking
- [ ] Privacy banner appears and functions
- [ ] Console shows no errors
- [ ] Shopify Analytics is receiving data

---

## 🆘 Troubleshooting

### Common Issues

**GA4 not tracking:**
- Check Measurement ID is correct
- Verify privacy banner is accepted
- Check browser console for errors

**GTM not loading:**
- Verify Container ID is correct
- Check Content Security Policy allows GTM domains
- Test in incognito mode

**E-commerce events missing:**
- Check Shopify Analytics integration
- Verify cart and checkout flows
- Test with GA4 DebugView

---

## 📞 Support

For additional help:
- **Google Analytics Help**: [support.google.com/analytics](https://support.google.com/analytics)
- **GTM Help**: [support.google.com/tagmanager](https://support.google.com/tagmanager)
- **Shopify Analytics**: [help.shopify.com/en/manual/reports-and-analytics](https://help.shopify.com/en/manual/reports-and-analytics)

---

## 🎉 You're Ready!

Once you've completed all steps and verified tracking is working, your Big River Coffee website will have comprehensive analytics tracking for:

- **Customer behavior** and journey mapping
- **E-commerce performance** and conversion tracking
- **Subscription analytics** and retention metrics
- **Coffee product insights** and preferences
- **GDPR-compliant** data collection

Happy tracking! ☕📊
