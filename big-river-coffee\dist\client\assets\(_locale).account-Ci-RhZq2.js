import{w as n}from"./with-props-CE_bzIRz.js";import{j as e}from"./jsx-runtime-CWqDQG74.js";import{u as a,O as i,F as l,N as o}from"./chunk-D4RADZKF-CZTShXQu.js";function u(){return!0}const g=n(function(){const{customer:r}=a(),t=r?r.firstName?`Welcome, ${r.firstName}`:"Welcome to your account":"Account Details";return e.jsx("div",{className:"min-h-screen",style:{backgroundColor:"#f97316"},children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[e.jsx("div",{className:"bg-white shadow-sm rounded-lg mb-6 p-6 border border-gray-100",children:e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"mb-4 md:mb-0",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:t}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Manage your account details and orders"})]}),e.jsx(c,{})]})}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-6",children:[e.jsx("div",{className:"w-full md:w-64 flex-shrink-0",children:e.jsx("div",{className:"bg-white shadow-sm rounded-lg p-4 border border-gray-100 sticky top-[calc(var(--header-height)+1rem)]",children:e.jsx(d,{})})}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"bg-white shadow-sm rounded-lg p-6 border border-gray-100",children:e.jsx(i,{context:{customer:r}})})})]})]})})});function d(){return e.jsxs("nav",{className:"flex flex-col space-y-1",role:"navigation",children:[e.jsx(o,{to:"/account/orders",className:({isActive:s,isPending:r})=>`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s?"bg-army-600 !text-white":r?"bg-gray-100 text-gray-500":"text-gray-700 hover:bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("svg",{className:"mr-3 h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),"Orders"]})}),e.jsx(o,{to:"/account/profile",className:({isActive:s,isPending:r})=>`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s?"bg-army-600 !text-white":r?"bg-gray-100 text-gray-500":"text-gray-700 hover:bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("svg",{className:"mr-3 h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Profile"]})}),e.jsxs("a",{href:"https://bigriverbe.myshopify.com/a/subscriptions/login",target:"_blank",rel:"noopener noreferrer",className:"px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-700 hover:bg-gray-50 flex items-center",children:[e.jsx("svg",{className:"mr-3 h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Manage Subscriptions",e.jsx("svg",{className:"ml-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]}),e.jsx(o,{to:"/account/addresses",className:({isActive:s,isPending:r})=>`px-4 py-2 rounded-md text-sm font-medium transition-colors ${s?"bg-army-600 !text-white":r?"bg-gray-100 text-gray-500":"text-gray-700 hover:bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs("svg",{className:"mr-3 h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Addresses"]})})]})}function c(){return e.jsx(l,{method:"POST",action:"/account/logout",children:e.jsxs("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-army-600 transition-colors",children:[e.jsx("svg",{className:"mr-2 -ml-1 h-5 w-5 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Sign out"]})})}export{g as default,u as shouldRevalidate};
