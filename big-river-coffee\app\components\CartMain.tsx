import {useOptimisticCart} from '@shopify/hydrogen';
import {Link} from 'react-router';
import type {CartApiQueryFragment} from 'storefrontapi.generated';
import {useAside} from '~/components/Aside';
import {CartLineItem} from '~/components/CartLineItem';
import {CartSummary} from './CartSummary';

export type CartLayout = 'page' | 'aside';

export type CartMainProps = {
  cart: CartApiQueryFragment | null;
  layout: CartLayout;
};

/**
 * The main cart component that displays the cart items and summary.
 * It is used by both the /cart route and the cart aside dialog.
 */
export function CartMain({layout, cart: originalCart}: CartMainProps) {
  // The useOptimisticCart hook applies pending actions to the cart
  // so the user immediately sees feedback when they modify the cart.
  const cart = useOptimisticCart(originalCart);

  const linesCount = Boolean(cart?.lines?.nodes?.length || 0);
  const withDiscount =
    cart &&
    Boolean(cart?.discountCodes?.filter((code) => code.applicable)?.length);
  const className = `cart-main ${withDiscount ? 'with-discount' : ''}`;
  const cartHasItems = cart?.totalQuantity ? cart.totalQuantity > 0 : false;

  return (
    <div className={`${className} h-full flex flex-col bg-gray-50`}>
      <CartEmpty hidden={linesCount} layout={layout} />
      {linesCount && (
        <>
          {/* Cart Items - Scrollable with proper bottom margin */}
          <div className="flex-1 overflow-y-auto p-4 pb-0">
            <div aria-labelledby="cart-lines" className="space-y-4 pb-4">
              {(cart?.lines?.nodes ?? []).map((line) => (
                <CartLineItem key={line.id} line={line} layout={layout} />
              ))}
            </div>
          </div>

          {/* Cart Summary - Fixed at bottom with proper spacing */}
          {cartHasItems && (
            <div className="flex-shrink-0 border-t border-army-200 bg-white p-4 mt-auto">
              <CartSummary cart={cart} layout={layout} />
            </div>
          )}
        </>
      )}
    </div>
  );
}

function CartEmpty({
  hidden = false,
}: {
  hidden: boolean;
  layout?: CartMainProps['layout'];
}) {
  const {close} = useAside();
  return (
    <div hidden={hidden} className="h-full flex flex-col items-center justify-center px-6 py-8 text-center bg-gray-50">
      <div className="flex flex-col items-center max-w-sm mx-auto">
        {/* Empty cart icon */}
        <div className="w-16 h-16 bg-army-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-army-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
        </div>

        <h3 className="text-lg font-bold text-gray-900 mb-2">Your cart is empty</h3>
        <p className="text-sm text-gray-600 mb-6 leading-relaxed">
          Looks like you haven&rsquo;t added anything yet. Let&rsquo;s get you started with some amazing coffee!
        </p>

        <Link
          to="/collections/all"
          onClick={close}
          prefetch="viewport"
          className="inline-flex items-center px-5 py-2.5 bg-army-600 hover:bg-army-700 transition-colors duration-200 font-medium rounded-lg"
          style={{ color: 'white' }}
        >
          <svg className="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Start Shopping
        </Link>
      </div>
    </div>
  );
}
