import { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  webpSrc?: string; // WebP version for better compression
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  width?: number;
  height?: number;
  loading?: 'eager' | 'lazy';
  sizes?: string;
  priority?: boolean; // For critical images that should load immediately
  onLoad?: () => void;
  onError?: () => void;
}

export function OptimizedImage({
  src,
  webpSrc,
  alt,
  className = '',
  style = {},
  width,
  height,
  loading = 'lazy',
  sizes,
  priority = false,
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [hasLoaded, setHasLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority || loading === 'eager');
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || loading === 'eager' || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before the image comes into view
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, loading, isInView]);

  const handleLoad = () => {
    setHasLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate WebP src if not provided
  const autoWebpSrc = webpSrc || src.replace(/\.(png|jpg|jpeg)$/i, '.webp');

  // Check if WebP is supported (simple check)
  const supportsWebP = typeof window !== 'undefined' && 
    document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0;

  // Use WebP if supported and available, otherwise fallback to original
  const imageSrc = (supportsWebP && !hasError) ? autoWebpSrc : src;

  return (
    <div className="relative" style={style}>
      {/* Loading placeholder */}
      {!hasLoaded && !hasError && (
        <div 
          className={`${className} bg-gray-200 animate-pulse`}
          style={{ 
            width: width || '100%', 
            height: height || '100%',
            aspectRatio: width && height ? `${width}/${height}` : undefined
          }}
        />
      )}

      {/* Optimized image with WebP support */}
      {isInView && (
        <picture>
          {/* WebP source for modern browsers */}
          {webpSrc && (
            <source srcSet={webpSrc} type="image/webp" />
          )}
          
          {/* Fallback image */}
          <img
            ref={imgRef}
            src={imageSrc}
            alt={alt}
            className={`${className} ${!hasLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
            width={width}
            height={height}
            loading={loading}
            sizes={sizes}
            onLoad={handleLoad}
            onError={handleError}
            style={{
              ...style,
              objectFit: style.objectFit || 'cover'
            }}
          />
        </picture>
      )}
    </div>
  );
}

/**
 * Utility function to generate WebP src from regular image src
 */
export function getWebPSrc(src: string): string {
  return src.replace(/\.(png|jpg|jpeg)$/i, '.webp');
}

/**
 * Hook to check WebP support
 */
export function useWebPSupport(): boolean {
  const [supportsWebP, setSupportsWebP] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const dataURL = canvas.toDataURL('image/webp');
    setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
  }, []);

  return supportsWebP;
}

/**
 * Critical Image component for above-the-fold images
 */
export function CriticalImage(props: Omit<OptimizedImageProps, 'loading' | 'priority'>) {
  return (
    <OptimizedImage
      {...props}
      loading="eager"
      priority={true}
    />
  );
}

/**
 * Responsive Hero Image component optimized for mobile performance
 */
interface ResponsiveHeroImageProps {
  src: string;
  mobileSrc?: string;
  webpSrc?: string;
  mobileWebpSrc?: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  sizes?: string;
  priority?: boolean;
}

export function ResponsiveHeroImage({
  src,
  mobileSrc,
  webpSrc,
  mobileWebpSrc,
  alt,
  className = '',
  style = {},
  sizes = '100vw',
  priority = true,
}: ResponsiveHeroImageProps) {
  // Auto-generate WebP sources if not provided
  const autoWebpSrc = webpSrc || src.replace(/\.(png|jpg|jpeg)$/i, '.webp');
  const autoMobileWebpSrc = mobileWebpSrc || (mobileSrc ? mobileSrc.replace(/\.(png|jpg|jpeg)$/i, '.webp') : autoWebpSrc);
  const finalMobileSrc = mobileSrc || src;

  return (
    <picture>
      {/* Mobile WebP */}
      <source
        media="(max-width: 768px)"
        srcSet={autoMobileWebpSrc}
        type="image/webp"
        sizes={sizes}
      />

      {/* Desktop WebP */}
      <source
        media="(min-width: 769px)"
        srcSet={autoWebpSrc}
        type="image/webp"
        sizes={sizes}
      />

      {/* Mobile fallback */}
      <source
        media="(max-width: 768px)"
        srcSet={finalMobileSrc}
        sizes={sizes}
      />

      {/* Desktop fallback */}
      <img
        src={src}
        alt={alt}
        className={className}
        style={{ objectFit: 'cover', ...style }}
        loading={priority ? 'eager' : 'lazy'}
        sizes={sizes}
      />
    </picture>
  );
}

/**
 * Lazy Image component for below-the-fold images
 */
export function LazyImage(props: Omit<OptimizedImageProps, 'loading' | 'priority'>) {
  return (
    <OptimizedImage
      {...props}
      loading="lazy"
      priority={false}
    />
  );
}
