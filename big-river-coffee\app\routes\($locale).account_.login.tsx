import {
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from '@shopify/remix-oxygen';
import {Form, useActionData, useNavigation, Link, type MetaFunction} from 'react-router';
import {useState, useEffect} from 'react';

export const meta: MetaFunction = () => {
  return [{title: 'Login | Big River Coffee'}];
};

export async function loader({request, context}: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const error = url.searchParams.get('error');

  // Only check if user is logged in if we're not handling a returnTo
  // This avoids the slow auth check for most guest checkout flows
  try {
    const isLoggedIn = await context.customerAccount.isLoggedIn();
    if (isLoggedIn) {
      return redirect('/account');
    }
  } catch (error) {
    // If auth check fails, just continue to show login page
    console.log('Auth check failed, showing login page');
  }

  return {error};
}

export async function action({request, context}: ActionFunctionArgs) {
  const {session, customerAccount} = context;
  const formData = await request.formData();
  const action = formData.get('_action');

  // Handle guest checkout - optimized for speed
  if (action === 'guest') {
    // Get the returnTo URL from the session or default to the homepage
    const returnTo = session.get('returnTo') || '/';

    // Clear the returnTo from the session only if it exists
    if (session.has('returnTo')) {
      session.unset('returnTo');
      return redirect(returnTo, {
        headers: {
          'Set-Cookie': await session.commit(),
        },
      });
    }

    // If no returnTo in session, just redirect without session operations
    return redirect(returnTo);
  }

  // Handle normal login (redirect to Shopify's login page)
  // Get the current URL to use as the callback URL
  const url = new URL(request.url);
  const callbackUrl = `${url.origin}/account/authorize`;

  return customerAccount.login();
}

export default function Login() {
  const actionData = useActionData<{error?: string}>();
  const navigation = useNavigation();
  const isLoginSubmitting = navigation.state === 'submitting' && navigation.formData?.get('_action') !== 'guest';
  const isGuestSubmitting = navigation.state === 'submitting' && navigation.formData?.get('_action') === 'guest';

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f97316' }}>
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100">
          <div className="p-8">
            <div className="text-center mb-8">
              <span className="text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4">Account Access</span>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h1>
              <p className="text-gray-600">Sign in to your account or continue as guest</p>
            </div>

            {actionData?.error && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {actionData.error === 'authorization_failed'
                  ? 'Authorization failed. Please try again.'
                  : 'An error occurred. Please try again.'}
              </div>
            )}

            <div className="space-y-4">
              {/* Login with Shopify */}
              <Form method="post" className="space-y-4">
                <div>
                  <button
                    type="submit"
                    disabled={isLoginSubmitting}
                    className="group relative flex w-full justify-center rounded-md bg-army-600 px-4 py-3.5 text-sm font-semibold text-white hover:bg-army-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                      {isLoginSubmitting ? (
                        <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <svg className="h-5 w-5 text-white group-hover:text-gray-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      )}
                    </span>
                    {isLoginSubmitting ? 'Signing in...' : 'Sign in to your account'}
                  </button>
                </div>
              </Form>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">or</span>
                </div>
              </div>

              {/* Guest Checkout */}
              <Form method="post">
                <input type="hidden" name="_action" value="guest" />
                <button
                  type="submit"
                  disabled={isGuestSubmitting}
                  className="group relative flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-3.5 text-sm font-semibold text-gray-700 hover:bg-gray-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                    {isGuestSubmitting ? (
                      <svg className="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-500 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                  </span>
                  {isGuestSubmitting ? 'Continuing...' : 'Continue as guest'}
                </button>
              </Form>
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                New to Big River Coffee?{' '}
                <Link
                  to="/account/register"
                  className="font-medium text-army-600 hover:text-army-700 transition-colors duration-200"
                >
                  Create an account
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
