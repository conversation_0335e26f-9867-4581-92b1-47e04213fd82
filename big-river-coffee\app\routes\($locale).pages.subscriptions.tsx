import {type MetaFunction} from 'react-router';

export const meta: MetaFunction = () => {
  return [
    {title: 'Coffee Subscriptions | Big River Coffee'},
    {description: 'Never run out of coffee with Big River Coffee subscriptions. Save up to 15%, get free shipping, and enjoy flexible delivery options.'}
  ];
};

export default function SubscriptionsPage() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f97316' }}>
      {/* Subscription Page Image - Standalone */}
      <div className="py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <img
              src="/subscriptionpage.webp"
              alt="Coffee subscription page"
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>

      {/* Why Subscribe Image - Standalone */}
      <div className="py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <img
              src="/whysubscribe.webp"
              alt="Why subscribe to Big River Coffee"
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>



      {/* How It Works Section */}
      <div className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-2xl p-8 md:p-12">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">How It Works</h2>
              <div className="w-24 h-1 bg-orange-500 rounded mx-auto mb-6"></div>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Getting started with your Big River Coffee subscription is simple and straightforward.
              </p>
            </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Choose Your Product</h3>
              <p className="text-gray-600">
                Select between our premium coffee beans or convenient K-cups based on your brewing preference.
              </p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Set Your Schedule</h3>
              <p className="text-gray-600">
                Choose your delivery frequency: weekly, monthly, every 3 weeks, or every 6 weeks.
              </p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Select Quantity</h3>
              <p className="text-gray-600">
                Decide how much coffee you need for each delivery based on your consumption habits.
              </p>
            </div>

            {/* Step 4 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">
                4
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Enjoy & Manage</h3>
              <p className="text-gray-600">
                Sit back and enjoy fresh coffee delivered to your door. Manage your subscription anytime online.
              </p>
            </div>
          </div>
          </div>
        </div>
      </div>

      {/* Coffee Quality Section */}
      <div className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-2xl p-8 md:p-12 text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Suppliers of the Top 3% of World Coffee
            </h2>
            <div className="w-24 h-1 bg-orange-500 rounded mb-8 mx-auto"></div>
            <p className="text-lg text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto">
              Artisanally nurtured and meticulously cultivated at the source, our coffee beans carry the direct imprint of our dedicated farmers. Fueled by passion and inspired by the vast landscapes of coffee plantations, they harvest specialty coffee that meets the rigorous standards set by the Specialty Coffee Association (SCA).
            </p>
            <p className="text-lg text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto">
              Our farmers don't just receive a fair payout; they earn nearly double compared to the New York Stock Exchange rates. This deliberate choice ensures that their efforts reverberate, making a tangible impact in the communities influenced by the force of Big River Coffee.
            </p>
            <div className="bg-orange-50 p-6 rounded-lg border border-orange-200 max-w-md mx-auto">
              <h3 className="text-xl font-bold text-orange-800 mb-3">According to the Q-Grader of SCA</h3>
              <div className="text-3xl font-bold text-orange-600 mb-2">82 - 88 Pts</div>
              <p className="text-orange-700">Specialty coffee growing at 5,000 ft above sea level</p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-2xl p-8 md:p-12 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Ready to Start Your Coffee Adventure?
            </h2>
            <p className="text-lg text-gray-600 mb-10 max-w-2xl mx-auto">
              Join thousands of coffee lovers who never run out of their favorite brew. Start your subscription today and taste the difference.
            </p>
            <div className="mt-8">
              <a
                href="/collections/all?section=subscriptions"
                className="inline-block bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
                style={{ color: 'white' }}
              >
                Build Your Subscription
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
