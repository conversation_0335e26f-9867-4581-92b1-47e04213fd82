#!/usr/bin/env node

import sharp from 'sharp';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

const stillframes = [
  'homepage_stillframe',
  'aboutus_stillframe', 
  'shop_stillframe'
];

async function createWebPStillframes() {
  console.log('🖼️  Creating WebP versions of stillframes...\n');
  
  for (const name of stillframes) {
    const pngPath = path.join(projectRoot, 'public', 'newhomepage', `${name}.png`);
    const webpPath = path.join(projectRoot, 'public', 'newhomepage', `${name}.webp`);
    
    try {
      await sharp(pngPath)
        .webp({
          quality: 85,
          effort: 6
        })
        .toFile(webpPath);
      
      console.log(`✅ Created ${name}.webp`);
    } catch (error) {
      console.error(`❌ Error creating ${name}.webp:`, error.message);
    }
  }
  
  console.log('\n🎉 WebP stillframes created successfully!');
}

createWebPStillframes();
