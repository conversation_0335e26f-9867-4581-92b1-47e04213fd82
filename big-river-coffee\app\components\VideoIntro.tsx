import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface VideoIntroProps {
  onVideoEnd: () => void;
  onSkip: () => void;
}

export function VideoIntro({ onVideoEnd, onSkip }: VideoIntroProps) {
  const [showSkip, setShowSkip] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    // Mark as client-side rendered to prevent hydration mismatch
    setIsClient(true);

    // Detect mobile device - video is desktop only
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent);
    setIsMobile(isMobileDevice);

    if (isMobileDevice) {
      // Skip video immediately on mobile - video is desktop only
      onSkip();
      return;
    }

    // Desktop only: Prevent scrolling during video and ensure we're at top
    document.body.style.overflow = 'hidden';
    document.body.classList.add('video-playing');
    // Force scroll to absolute top
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    window.scrollTo(0, 0);

    // Desktop only: Show skip button after 2 seconds
    const skipTimer = setTimeout(() => {
      setShowSkip(true);
    }, 2000);

    return () => {
      clearTimeout(skipTimer);
      // Re-enable scrolling when component unmounts
      document.body.style.overflow = 'unset';
      document.body.classList.remove('video-playing');
    };
  }, [onSkip]);

  const handleVideoEnd = () => {
    // Re-enable scrolling and remove video class
    document.body.style.overflow = 'unset';
    document.body.classList.remove('video-playing');
    // Start fadeout animation
    setIsVisible(false);
    // Immediately call parent - let parent handle timing
    onVideoEnd();
  };

  const handleSkip = () => {
    // Re-enable scrolling and remove video class
    document.body.style.overflow = 'unset';
    document.body.classList.remove('video-playing');
    // Start fadeout animation
    setIsVisible(false);
    // Immediately call parent - let parent handle timing
    onSkip();
  };

  const handleVideoLoad = () => {
    // Video loaded successfully
    console.log('Big River Coffee intro video loaded');
  };

  const handleVideoError = () => {
    console.log('Video failed to load, skipping to homepage');
    // Auto-skip on error
    setTimeout(() => {
      handleSkip();
    }, 1000);
  };

  const handleCanPlay = () => {
    // Video can play - desktop only
    console.log('Big River Coffee intro video ready to play');
  };

  // Don't render anything on mobile - video is desktop only
  if (isMobile) {
    return null;
  }

  // Only render if we're in the browser
  if (typeof document === 'undefined') {
    return null;
  }

  // Don't render anything until client-side to prevent hydration mismatch
  if (!isClient) {
    return null;
  }

  // Don't render anything on mobile - just skip
  if (isMobile) {
    return null;
  }

  const videoElement = (
    <div
      className={`transition-opacity duration-1000 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'black',
        zIndex: 999999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {/* Video element */}
      {isClient && (
        <video
          ref={videoRef}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          }}
          autoPlay
          muted
          playsInline
          preload="metadata"
          onLoadedData={handleVideoLoad}
          onEnded={handleVideoEnd}
          onError={handleVideoError}
          onCanPlay={handleCanPlay}
        >
          <source src="/brintroc.mp4" type="video/mp4" />
          <source src="/herovidbr.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      )}

      {/* Skip button with Big River Coffee styling */}
      {showSkip && (
        <button
          onClick={handleSkip}
          className={`absolute top-6 right-6 bg-army-600 bg-opacity-90 hover:bg-army-700 text-white px-6 py-3 rounded-lg
            hover:bg-opacity-100 transition-all duration-300 transform hover:scale-105 font-medium shadow-lg
            ${isVisible ? 'opacity-100' : 'opacity-0'}
          `}
        >
          Skip Intro
        </button>
      )}

      {/* Big River Coffee logo overlay */}
      <div
        className={`absolute bottom-8 left-8 transition-opacity duration-1000 ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <img
          src="/headerlogo.svg"
          alt="Big River Coffee"
          className="h-12 w-auto opacity-80"
        />
      </div>
    </div>
  );

  // Use portal to render video at document body level
  return createPortal(videoElement, document.body);
}
