import{w as e}from"./with-props-CE_bzIRz.js";import{j as o}from"./jsx-runtime-CWqDQG74.js";import{u as r,L as t}from"./chunk-D4RADZKF-CZTShXQu.js";const p=({data:i})=>[{title:`Hydrogen | ${(i==null?void 0:i.policy.title)??""}`}],m=e(function(){const{policy:s}=r();return o.jsxs("div",{className:"policy",children:[o.jsx("br",{}),o.jsx("br",{}),o.jsx("div",{children:o.jsx(t,{to:"/policies",children:"← Back to Policies"})}),o.jsx("br",{}),o.jsx("h1",{children:s.title}),o.jsx("div",{dangerouslySetInnerHTML:{__html:s.body}})]})});export{m as default,p as meta};
