import { useEffect, useRef, useState } from 'react';

// Custom hook for mobile detection
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      // Check viewport width
      const viewportWidth = window.innerWidth;
      // Check user agent for mobile devices
      const userAgent = navigator.userAgent.toLowerCase();
      const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'tablet'];
      const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

      // Consider mobile if viewport is small OR user agent indicates mobile
      setIsMobile(viewportWidth <= 768 || isMobileUA);
    };

    // Check on mount
    checkIsMobile();

    // Check on resize
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

interface OptimizedVideoProps {
  src: string;
  fallbackImage?: string;
  poster?: string; // Stillframe image for immediate display
  className?: string;
  style?: React.CSSProperties;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  playsInline?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  lazy?: boolean;
  forceMobileImage?: boolean; // Force image-only mode on mobile for performance
  onLoadStart?: () => void;
  onCanPlay?: () => void;
  onError?: () => void;
}

export function OptimizedVideo({
  src,
  fallbackImage,
  poster,
  className = '',
  style = {},
  autoPlay = true,
  muted = true,
  loop = true,
  playsInline = true,
  preload = 'metadata',
  lazy = true,
  forceMobileImage = true, // Default to true for better mobile performance
  onLoadStart,
  onCanPlay,
  onError,
}: OptimizedVideoProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isInView, setIsInView] = useState(!lazy);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [canPlayThrough, setCanPlayThrough] = useState(false);

  // Mobile detection hook
  const isMobile = useIsMobile();

  // Determine if we should load video or just show image
  const shouldLoadVideo = !isMobile || !forceMobileImage;
  const displayImage = poster || fallbackImage;

  // Intersection Observer for lazy loading - only if we're loading video
  useEffect(() => {
    if (!lazy || isInView || !shouldLoadVideo) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before the video comes into view
        threshold: 0.1,
      }
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView, shouldLoadVideo]);

  // Handle video events - only if we're loading video
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !shouldLoadVideo) return;

    const handleLoadStart = () => {
      onLoadStart?.();
    };

    const handleCanPlay = () => {
      setHasLoaded(true);
      onCanPlay?.();
    };

    const handleCanPlayThrough = () => {
      setCanPlayThrough(true);
    };

    const handleLoadedData = () => {
      // Video has loaded enough data to start playing
      setHasLoaded(true);
    };

    const handleError = () => {
      setHasError(true);
      onError?.();
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('canplaythrough', handleCanPlayThrough);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('canplaythrough', handleCanPlayThrough);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [onLoadStart, onCanPlay, onError, shouldLoadVideo]);

  // Auto-play when video is ready and in view - only if we're loading video
  useEffect(() => {
    if (hasLoaded && autoPlay && videoRef.current && !isPlaying && shouldLoadVideo) {
      const playPromise = videoRef.current.play();
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.warn('Video autoplay failed:', error);
          // If autoplay fails, still show the video (without playing)
          setIsPlaying(false);
        });
      }
    }
  }, [hasLoaded, autoPlay, isPlaying, shouldLoadVideo]);

  // Force video load for non-lazy videos (like homepage background) - only if we're loading video
  useEffect(() => {
    if (!lazy && videoRef.current && isInView && shouldLoadVideo) {
      const video = videoRef.current;
      // Force load the video
      video.load();

      // Try to play immediately if autoplay is enabled
      if (autoPlay) {
        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.warn('Initial video autoplay failed:', error);
          });
        }
      }
    }
  }, [lazy, autoPlay, isInView, shouldLoadVideo]);

  // Pause video when out of view to save bandwidth - only if we're loading video
  useEffect(() => {
    if (!videoRef.current || !shouldLoadVideo) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (videoRef.current) {
            if (entry.isIntersecting) {
              if (autoPlay && hasLoaded) {
                videoRef.current.play().catch(() => {
                  // Ignore autoplay failures
                });
                setIsPlaying(true);
              }
            } else {
              videoRef.current.pause();
              setIsPlaying(false);
            }
          }
        });
      },
      {
        threshold: 0.3, // Pause when less than 30% visible for better performance
        rootMargin: '50px', // Start playing 50px before entering viewport
      }
    );

    observer.observe(videoRef.current);

    return () => observer.disconnect();
  }, [autoPlay, hasLoaded, shouldLoadVideo]);

  // Early return for error state
  if (hasError && displayImage) {
    return (
      <img
        src={displayImage}
        alt="Video fallback"
        className={className}
        style={style}
      />
    );
  }

  // Mobile optimization: Show only image on mobile devices for better performance
  if (!shouldLoadVideo) {
    return (
      <div className="relative" style={style}>
        {displayImage ? (
          <picture>
            <source srcSet={displayImage.replace(/\.(png|jpg|jpeg)$/i, '.webp')} type="image/webp" />
            <img
              src={displayImage}
              alt="Video poster"
              className={className}
              style={{ objectFit: 'cover' }}
              loading="eager" // Load critical images immediately
            />
          </picture>
        ) : (
          <div className={`${className} bg-gray-200 flex items-center justify-center`}>
            <span className="text-gray-500">Video not available</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative" style={style}>
      {/* Poster/Loading placeholder - shows immediately for better UX */}
      {!hasLoaded && displayImage && (
        <picture>
          <source srcSet={displayImage.replace(/\.(png|jpg|jpeg)$/i, '.webp')} type="image/webp" />
          <img
            src={displayImage}
            alt="Loading..."
            className={`${className} absolute inset-0 w-full h-full object-cover z-10`}
            style={{ objectFit: 'cover' }}
            loading="eager" // Load poster images immediately
          />
        </picture>
      )}

      {/* Video element - only loads on desktop */}
      {isInView && (
        <video
          ref={videoRef}
          className={`${className} ${!hasLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-500 ease-in-out`}
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          playsInline={playsInline}
          preload={preload}
          poster={poster} // Native poster attribute for better browser support
          style={{ objectFit: 'cover' }}
        >
          <source src={src} type="video/mp4" />
          {displayImage && (
            <img
              src={displayImage}
              alt="Video not supported"
              className="w-full h-full object-cover"
            />
          )}
        </video>
      )}
    </div>
  );
}
