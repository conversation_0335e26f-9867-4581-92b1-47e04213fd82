import { Link } from 'react-router';
import { UTMParameters, createUTMURL } from '~/lib/utm';

interface UTMLinkProps {
  to: string;
  utmParams: UTMParameters;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  external?: boolean;
}

/**
 * UTM-enabled Link component
 * Automatically appends UTM parameters to internal and external links
 */
export function UTMLink({ 
  to, 
  utmParams, 
  children, 
  className, 
  onClick,
  external = false 
}: UTMLinkProps) {
  if (external) {
    // External link with UTM parameters - only create URL on client side
    let utmUrl = to;
    if (typeof window !== 'undefined') {
      utmUrl = createUTMURL(to, utmParams);
    }

    return (
      <a
        href={utmUrl}
        className={className}
        onClick={onClick}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    );
  }

  // Internal link - UTM parameters will be captured by useUTMTracking
  // Build URL safely for SSR
  let linkTo = to;
  if (typeof window !== 'undefined') {
    const url = new URL(to, window.location.origin);
    Object.entries(utmParams).forEach(([key, value]) => {
      if (value) {
        url.searchParams.set(key, value);
      }
    });
    linkTo = url.pathname + url.search;
  }

  return (
    <Link
      to={linkTo}
      className={className}
      onClick={onClick}
    >
      {children}
    </Link>
  );
}

/**
 * Pre-configured UTM links for common Big River Coffee campaigns
 */
export const UTMLinks = {
  // Email campaign links
  EmailCTA: ({ children, className, to = '/collections/all' }: { 
    children: React.ReactNode; 
    className?: string; 
    to?: string;
  }) => (
    <UTMLink
      to={to}
      utmParams={{
        utm_source: 'email',
        utm_medium: 'email',
        utm_campaign: 'newsletter',
      }}
      className={className}
    >
      {children}
    </UTMLink>
  ),

  // Social media links
  SocialCTA: ({ 
    children, 
    className, 
    platform = 'social',
    to = '/collections/all' 
  }: { 
    children: React.ReactNode; 
    className?: string; 
    platform?: string;
    to?: string;
  }) => (
    <UTMLink
      to={to}
      utmParams={{
        utm_source: platform,
        utm_medium: 'social',
        utm_campaign: 'social_promotion',
      }}
      className={className}
    >
      {children}
    </UTMLink>
  ),

  // Popup/Banner CTA
  PopupCTA: ({ children, className, to = '/collections/all' }: { 
    children: React.ReactNode; 
    className?: string; 
    to?: string;
  }) => (
    <UTMLink
      to={to}
      utmParams={{
        utm_source: 'website',
        utm_medium: 'popup',
        utm_campaign: 'promo_popup',
        utm_content: 'shop_coffee_button',
      }}
      className={className}
    >
      {children}
    </UTMLink>
  ),
};
