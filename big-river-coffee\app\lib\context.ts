import {createHydrogenContext} from '@shopify/hydrogen';
import {createAdminApiClient} from '@shopify/admin-api-client';
import {AppSession} from '~/lib/session';
import {CART_QUERY_FRAGMENT} from '~/lib/fragments';
import {getLocaleFromRequest} from '~/lib/i18n';

/**
 * The context implementation is separate from server.ts
 * so that type can be extracted for AppLoadContext
 * */
export async function createAppLoadContext(
  request: Request,
  env: Env,
  executionContext: ExecutionContext,
) {
  /**
   * Open a cache instance in the worker and a custom session instance.
   */
  if (!env?.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  const waitUntil = executionContext.waitUntil.bind(executionContext);
  const [cache, session] = await Promise.all([
    caches.open('hydrogen'),
    AppSession.init(request, [env.SESSION_SECRET]),
  ]);

  const hydrogenContext = createHydrogenContext({
    env,
    request,
    cache,
    waitUntil,
    session,
    i18n: getLocaleFromRequest(request),
    cart: {
      queryFragment: CART_QUERY_FRAGMENT,
    },
    customerAccount: {
      // Enable B2B features for guest checkout
      unstableB2b: true,
    },
  });

  // Create Admin API client for form submissions
  const admin = createAdminApiClient({
    storeDomain: (env as any).PUBLIC_STORE_DOMAIN,
    apiVersion: '2025-01',
    accessToken: (env as any).PRIVATE_ADMIN_API_TOKEN,
  });

  return {
    ...hydrogenContext,
    admin,
    // declare additional Remix loader context
  };
}
