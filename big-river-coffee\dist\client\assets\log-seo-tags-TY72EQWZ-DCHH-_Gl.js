function g({headTags:n}){return a(n),null}var l="text-transform: uppercase;",c="text-transform: uppercase; font-weight: bold; text-transform: uppercase;font-weight: bold";function a(n){console.log(" "),console.log("%cSEO Meta Tags",`${c}`),console.log(" "),n.forEach(o=>{if(o.tag==="script"){if(console.log("%c• JSON LD ",l),o.children)try{console.table(JSON.parse(o.children),["name","content"])}catch{console.log(o.children)}}else{if(console.log(`%c• ${o.tag} `,l),o.children)if(typeof o.children=="string")console.log(`↳ ${o.children}`);else try{Object.entries(JSON.parse(o.children)).map(([t,e])=>console.log(`↳ ${e}`))}catch{console.log(o.children)}if(o.props.property==="og:image:url"){let t=o.props.content;s(t).then(e=>{let r=`font-size: 400px; padding: 10px; background: white url(${e}) no-repeat center; background-size: contain;`;console.log("%c• Share image preview",l),console.log("%c  ",r),console.log(`↳ ${t}`)}).catch(e=>{console.error(e)})}Object.entries(o.props).map(([t,e])=>{console.log(`↳ ${t} → ${e}`)})}console.log(" ")})}async function s(n){let o=await(await(await fetch(n)).blob()).arrayBuffer();return`data:image/png;base64,${i(o)}`}function i(n){let o="",t=new Uint8Array(n),e=t.byteLength;for(let r=0;r<e;r++)o+=String.fromCharCode(t[r]);return btoa(o)}export{g as default,a as logSeoTags};
