import{w as l}from"./with-props-CE_bzIRz.js";import{j as s}from"./jsx-runtime-CWqDQG74.js";import{u as a}from"./chunk-D4RADZKF-CZTShXQu.js";import{M as d}from"./Money-St0DOtgu.js";import{I as x}from"./Image-83A1PdZQ.js";const w=({data:e})=>{var r;return[{title:`Order ${(r=e==null?void 0:e.order)==null?void 0:r.name}`}]},S=l(function(){const{order:r,lineItems:c,discountValue:t,discountPercentage:i,fulfillmentStatus:n}=a();return s.jsxs("div",{className:"account-order",children:[s.jsxs("h2",{children:["Order ",r.name]}),s.jsxs("p",{children:["Placed on ",new Date(r.processedAt).toDateString()]}),s.jsx("br",{}),s.jsxs("div",{children:[s.jsxs("table",{children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{scope:"col",children:"Product"}),s.jsx("th",{scope:"col",children:"Price"}),s.jsx("th",{scope:"col",children:"Quantity"}),s.jsx("th",{scope:"col",children:"Total"})]})}),s.jsx("tbody",{children:c.map((h,o)=>s.jsx(j,{lineItem:h},o))}),s.jsxs("tfoot",{children:[(t&&t.amount||i)&&s.jsxs("tr",{children:[s.jsx("th",{scope:"row",colSpan:3,children:s.jsx("p",{children:"Discounts"})}),s.jsx("th",{scope:"row",children:s.jsx("p",{children:"Discounts"})}),s.jsx("td",{children:i?s.jsxs("span",{children:["-",i,"% OFF"]}):t&&s.jsx(d,{data:t})})]}),s.jsxs("tr",{children:[s.jsx("th",{scope:"row",colSpan:3,children:s.jsx("p",{children:"Subtotal"})}),s.jsx("th",{scope:"row",children:s.jsx("p",{children:"Subtotal"})}),s.jsx("td",{children:s.jsx(d,{data:r.subtotal})})]}),s.jsxs("tr",{children:[s.jsx("th",{scope:"row",colSpan:3,children:"Tax"}),s.jsx("th",{scope:"row",children:s.jsx("p",{children:"Tax"})}),s.jsx("td",{children:s.jsx(d,{data:r.totalTax})})]}),s.jsxs("tr",{children:[s.jsx("th",{scope:"row",colSpan:3,children:"Total"}),s.jsx("th",{scope:"row",children:s.jsx("p",{children:"Total"})}),s.jsx("td",{children:s.jsx(d,{data:r.totalPrice})})]})]})]}),s.jsxs("div",{children:[s.jsx("h3",{children:"Shipping Address"}),r!=null&&r.shippingAddress?s.jsxs("address",{children:[s.jsx("p",{children:r.shippingAddress.name}),r.shippingAddress.formatted?s.jsx("p",{children:r.shippingAddress.formatted}):"",r.shippingAddress.formattedArea?s.jsx("p",{children:r.shippingAddress.formattedArea}):""]}):s.jsx("p",{children:"No shipping address defined"}),s.jsx("h3",{children:"Status"}),s.jsx("div",{children:s.jsx("p",{children:n})})]})]}),s.jsx("br",{}),s.jsx("p",{children:s.jsx("a",{target:"_blank",href:r.statusPageUrl,rel:"noreferrer",children:"View Order Status →"})})]})});function j({lineItem:e}){return s.jsxs("tr",{children:[s.jsx("td",{children:s.jsxs("div",{children:[(e==null?void 0:e.image)&&s.jsx("div",{children:s.jsx(x,{data:e.image,width:96,height:96})}),s.jsxs("div",{children:[s.jsx("p",{children:e.title}),s.jsx("small",{children:e.variantTitle})]})]})}),s.jsx("td",{children:s.jsx(d,{data:e.price})}),s.jsx("td",{children:e.quantity}),s.jsx("td",{children:s.jsx(d,{data:e.totalDiscount})})]},e.id)}export{S as default,w as meta};
