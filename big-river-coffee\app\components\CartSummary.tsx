import type {CartApiQueryFragment} from 'storefrontapi.generated';
import type {CartLayout} from '~/components/CartMain';
import {CartForm, Money, type OptimisticCart} from '@shopify/hydrogen';
import {useRef} from 'react';
import {FetcherWithComponents} from 'react-router';

type CartSummaryProps = {
  cart: OptimisticCart<CartApiQueryFragment | null>;
  layout: CartLayout;
};

export function CartSummary({cart, layout}: CartSummaryProps) {
  return (
    <div className="bg-army-50 rounded-lg p-3 border border-army-100 space-y-2">
      <h4 className="text-base font-bold text-army-900">Order Summary</h4>

      <div className="space-y-2">
        {/* Subtotal */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Subtotal</span>
          <span className="text-sm font-medium text-gray-900">
            {cart.cost?.subtotalAmount?.amount ? (
              <Money data={cart.cost?.subtotalAmount} />
            ) : (
              '-'
            )}
          </span>
        </div>

        {/* Tax (if available) */}
        {cart.cost?.totalTaxAmount?.amount && parseFloat(cart.cost.totalTaxAmount.amount) > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Tax</span>
            <span className="text-sm font-medium text-gray-900">
              <Money data={cart.cost.totalTaxAmount} />
            </span>
          </div>
        )}

        {/* Shipping */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Shipping</span>
          <span className="text-xs text-gray-500">Calculated at checkout</span>
        </div>

        {/* Divider */}
        <div className="border-t border-army-200 pt-2">
          <div className="flex justify-between items-center">
            <span className="text-base font-bold text-army-900">Total</span>
            <span className="text-base font-bold text-army-600">
              {cart.cost?.totalAmount?.amount ? (
                <Money data={cart.cost.totalAmount} />
              ) : cart.cost?.subtotalAmount?.amount ? (
                <Money data={cart.cost.subtotalAmount} />
              ) : (
                '-'
              )}
            </span>
          </div>
        </div>
      </div>

      <CartCheckoutActions checkoutUrl={cart.checkoutUrl} />
    </div>
  );
}
function CartCheckoutActions({checkoutUrl}: {checkoutUrl?: string}) {
  if (!checkoutUrl) return null;

  return (
    <div className="pt-2 border-t border-army-200">
      <a
        href={checkoutUrl}
        target="_self"
        className="block w-full bg-army-600 hover:bg-army-700 transition-colors duration-200 font-medium text-base text-center py-3 px-4 rounded-lg"
        style={{ color: 'white' }}
      >
        Continue to Checkout
        <svg className="inline-block w-4 h-4 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      </a>
    </div>
  );
}

function CartDiscounts({
  discountCodes,
}: {
  discountCodes?: CartApiQueryFragment['discountCodes'];
}) {
  const codes: string[] =
    discountCodes
      ?.filter((discount) => discount.applicable)
      ?.map(({code}) => code) || [];

  return (
    <div className="space-y-4">
      {/* Applied Discounts */}
      {codes.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h5 className="text-sm font-medium text-green-800">Discount Applied</h5>
              <p className="text-sm text-green-600 font-mono">{codes.join(', ')}</p>
            </div>
            <UpdateDiscountForm>
              <button
                type="submit"
                className="text-green-700 hover:text-green-900 text-sm font-medium"
              >
                Remove
              </button>
            </UpdateDiscountForm>
          </div>
        </div>
      )}

      {/* Discount Code Input */}
      <UpdateDiscountForm discountCodes={codes}>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Discount Code
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              name="discountCode"
              placeholder="Enter discount code"
              className="flex-1 px-3 py-2 border border-army-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500"
            />
            <button
              type="submit"
              className="px-4 py-2 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium"
            >
              Apply
            </button>
          </div>
        </div>
      </UpdateDiscountForm>
    </div>
  );
}

function UpdateDiscountForm({
  discountCodes,
  children,
}: {
  discountCodes?: string[];
  children: React.ReactNode;
}) {
  return (
    <CartForm
      route="/cart"
      action={CartForm.ACTIONS.DiscountCodesUpdate}
      inputs={{
        discountCodes: discountCodes || [],
      }}
    >
      {children}
    </CartForm>
  );
}

function CartGiftCard({
  giftCardCodes,
}: {
  giftCardCodes: CartApiQueryFragment['appliedGiftCards'] | undefined;
}) {
  const appliedGiftCardCodes = useRef<string[]>([]);
  const giftCardCodeInput = useRef<HTMLInputElement>(null);
  const codes: string[] =
    giftCardCodes?.map(({lastCharacters}) => `***${lastCharacters}`) || [];

  function saveAppliedCode(code: string) {
    const formattedCode = code.replace(/\s/g, ''); // Remove spaces
    if (!appliedGiftCardCodes.current.includes(formattedCode)) {
      appliedGiftCardCodes.current.push(formattedCode);
    }
    giftCardCodeInput.current!.value = '';
  }

  function removeAppliedCode() {
    appliedGiftCardCodes.current = [];
  }

  return (
    <div>
      {/* Have existing gift card applied, display it with a remove option */}
      <dl hidden={!codes.length}>
        <div>
          <dt>Applied Gift Card(s)</dt>
          <UpdateGiftCardForm>
            <div className="cart-discount">
              <code>{codes?.join(', ')}</code>
              &nbsp;
              <button onSubmit={() => removeAppliedCode}>Remove</button>
            </div>
          </UpdateGiftCardForm>
        </div>
      </dl>

      {/* Show an input to apply a discount */}
      <UpdateGiftCardForm
        giftCardCodes={appliedGiftCardCodes.current}
        saveAppliedCode={saveAppliedCode}
      >
        <div>
          <input
            type="text"
            name="giftCardCode"
            placeholder="Gift card code"
            ref={giftCardCodeInput}
          />
          &nbsp;
          <button type="submit">Apply</button>
        </div>
      </UpdateGiftCardForm>
    </div>
  );
}

function UpdateGiftCardForm({
  giftCardCodes,
  saveAppliedCode,
  children,
}: {
  giftCardCodes?: string[];
  saveAppliedCode?: (code: string) => void;
  removeAppliedCode?: () => void;
  children: React.ReactNode;
}) {
  return (
    <CartForm
      route="/cart"
      action={CartForm.ACTIONS.GiftCardCodesUpdate}
      inputs={{
        giftCardCodes: giftCardCodes || [],
      }}
    >
      {(fetcher: FetcherWithComponents<any>) => {
        const code = fetcher.formData?.get('giftCardCode');
        if (code && saveAppliedCode) {
          saveAppliedCode(code as string);
        }
        return children;
      }}
    </CartForm>
  );
}
