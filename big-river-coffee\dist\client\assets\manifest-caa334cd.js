window.__reactRouterManifest={"entry":{"module":"/assets/entry.client-DtqebZFJ.js","imports":["/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-C2LW_5Ip.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":true,"module":"/assets/root-DEFjTE6O.js","imports":["/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-C2LW_5Ip.js","/assets/root-DJpprg3s.js","/assets/index-BYzZDKcn.js","/assets/with-props-CE_bzIRz.js","/assets/Aside-D-IBZjP3.js","/assets/CartMain-De5XrJ0a.js","/assets/variants-QTzM9WEo.js","/assets/ProductPrice-DA7E5Y22.js","/assets/Money-St0DOtgu.js","/assets/Image-83A1PdZQ.js","/assets/search-DOeYwaXi.js"],"css":[]},"routes/[robots.txt]":{"id":"routes/[robots.txt]","parentId":"root","path":"robots.txt","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_robots.txt_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale)":{"id":"routes/($locale)","parentId":"root","path":":locale?","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle.$articleHandle":{"id":"routes/($locale).blogs.$blogHandle.$articleHandle","parentId":"routes/($locale)","path":"blogs/:blogHandle/:articleHandle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._articleHandle-D4i19fm9.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/Image-83A1PdZQ.js"],"css":[]},"routes/($locale).api.$version.[graphql.json]":{"id":"routes/($locale).api.$version.[graphql.json]","parentId":"routes/($locale)","path":"api/:version/graphql.json","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api._version._graphql.json_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).sitemap.$type.$page[.xml]":{"id":"routes/($locale).sitemap.$type.$page[.xml]","parentId":"routes/($locale)","path":"sitemap/:type/:page.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).sitemap._type._page_.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle._index":{"id":"routes/($locale).blogs.$blogHandle._index","parentId":"routes/($locale)","path":"blogs/:blogHandle","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._index-BWEML3TL.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/PaginatedResourceSection-B1j0nYOx.js","/assets/Image-83A1PdZQ.js","/assets/index-BYzZDKcn.js"],"css":[]},"routes/($locale).collections.all-coffees":{"id":"routes/($locale).collections.all-coffees","parentId":"routes/($locale)","path":"collections/all-coffees","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-coffees-DXTRvTNt.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).products.roasters-box":{"id":"routes/($locale).products.roasters-box","parentId":"routes/($locale)","path":"products/roasters-box","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products.roasters-box-DyCo3VsR.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-BYzZDKcn.js","/assets/Aside-D-IBZjP3.js","/assets/AddToCartButton-Becbnk2X.js","/assets/getProductOptions-DRO1RMF1.js","/assets/Image-83A1PdZQ.js","/assets/Money-St0DOtgu.js","/assets/useSelectedOptionInUrlParam-Y-M7jAiB.js"],"css":[]},"routes/($locale).collections.$handle":{"id":"routes/($locale).collections.$handle","parentId":"routes/($locale)","path":"collections/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._handle-BOoxTBpX.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-BYzZDKcn.js","/assets/ProductCard-C4KfuSJu.js","/assets/variants-QTzM9WEo.js","/assets/AddToCartButton-Becbnk2X.js","/assets/Image-83A1PdZQ.js","/assets/Money-St0DOtgu.js"],"css":[]},"routes/($locale).pages.subscriptions":{"id":"routes/($locale).pages.subscriptions","parentId":"routes/($locale)","path":"pages/subscriptions","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.subscriptions-D9kAOjit.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).account_.authorize":{"id":"routes/($locale).account_.authorize","parentId":"routes/($locale)","path":"account/authorize","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.authorize-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections._index":{"id":"routes/($locale).collections._index","parentId":"routes/($locale)","path":"collections","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._index-B_wShGuK.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/PaginatedResourceSection-B1j0nYOx.js","/assets/Image-83A1PdZQ.js","/assets/index-BYzZDKcn.js"],"css":[]},"routes/($locale).account_.register":{"id":"routes/($locale).account_.register","parentId":"routes/($locale)","path":"account/register","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.register-BpSzz2c2.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).policies.$handle":{"id":"routes/($locale).policies.$handle","parentId":"routes/($locale)","path":"policies/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._handle-pcKgmfpq.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).products.$handle":{"id":"routes/($locale).products.$handle","parentId":"routes/($locale)","path":"products/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products._handle-Ab3jeieJ.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-BYzZDKcn.js","/assets/ProductPrice-DA7E5Y22.js","/assets/Image-83A1PdZQ.js","/assets/AddToCartButton-Becbnk2X.js","/assets/Aside-D-IBZjP3.js","/assets/Money-St0DOtgu.js","/assets/getProductOptions-DRO1RMF1.js","/assets/useSelectedOptionInUrlParam-Y-M7jAiB.js"],"css":[]},"routes/($locale).account_.logout":{"id":"routes/($locale).account_.logout","parentId":"routes/($locale)","path":"account/logout","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.logout-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections.all":{"id":"routes/($locale).collections.all","parentId":"routes/($locale)","path":"collections/all","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-DD2aMpfc.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/ProductCard-C4KfuSJu.js","/assets/AddToCartButton-Becbnk2X.js","/assets/Aside-D-IBZjP3.js","/assets/OptimizedVideo-Cwhh0DV1.js","/assets/scrollAnimations-D2F8dg76.js","/assets/Money-St0DOtgu.js","/assets/getProductOptions-DRO1RMF1.js","/assets/variants-QTzM9WEo.js","/assets/Image-83A1PdZQ.js","/assets/index-BYzZDKcn.js"],"css":[]},"routes/($locale).pages.nicaragua":{"id":"routes/($locale).pages.nicaragua","parentId":"routes/($locale)","path":"pages/nicaragua","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.nicaragua-8OAqouPG.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).policies._index":{"id":"routes/($locale).policies._index","parentId":"routes/($locale)","path":"policies","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._index-B7jaKOjb.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).account_.login":{"id":"routes/($locale).account_.login","parentId":"routes/($locale)","path":"account/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.login-CsORYGXx.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).api.newsletter":{"id":"routes/($locale).api.newsletter","parentId":"routes/($locale)","path":"api/newsletter","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api.newsletter-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).discount.$code":{"id":"routes/($locale).discount.$code","parentId":"routes/($locale)","path":"discount/:code","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).discount._code-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).pages.$handle":{"id":"routes/($locale).pages.$handle","parentId":"routes/($locale)","path":"pages/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages._handle-qzCLXXBw.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).[sitemap.xml]":{"id":"routes/($locale).[sitemap.xml]","parentId":"routes/($locale)","path":"sitemap.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._sitemap.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).api.products":{"id":"routes/($locale).api.products","parentId":"routes/($locale)","path":"api/products","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api.products-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs._index":{"id":"routes/($locale).blogs._index","parentId":"routes/($locale)","path":"blogs","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._index-Bbg2UjLm.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/PaginatedResourceSection-B1j0nYOx.js","/assets/index-BYzZDKcn.js"],"css":[]},"routes/($locale).pages.brew":{"id":"routes/($locale).pages.brew","parentId":"routes/($locale)","path":"pages/brew","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.brew-CbQQcQLv.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).affiliate":{"id":"routes/($locale).affiliate","parentId":"routes/($locale)","path":"affiliate","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).affiliate-DXvh1GTC.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).our-story":{"id":"routes/($locale).our-story","parentId":"routes/($locale)","path":"our-story","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).our-story-BQZtHPB6.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/OptimizedVideo-Cwhh0DV1.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).account":{"id":"routes/($locale).account","parentId":"routes/($locale)","path":"account","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account-Ci-RhZq2.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).account.orders._index":{"id":"routes/($locale).account.orders._index","parentId":"routes/($locale).account","path":"orders","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._index-CK6cXWeH.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/PaginatedResourceSection-B1j0nYOx.js","/assets/index-BYzZDKcn.js","/assets/Money-St0DOtgu.js"],"css":[]},"routes/($locale).account.orders.$id":{"id":"routes/($locale).account.orders.$id","parentId":"routes/($locale).account","path":"orders/:id","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._id-xHjcC9OM.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/Money-St0DOtgu.js","/assets/Image-83A1PdZQ.js"],"css":[]},"routes/($locale).account.addresses":{"id":"routes/($locale).account.addresses","parentId":"routes/($locale).account","path":"addresses","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.addresses-CRZVGZlY.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).account.profile":{"id":"routes/($locale).account.profile","parentId":"routes/($locale).account","path":"profile","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.profile-D4K2EQ-c.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).account._index":{"id":"routes/($locale).account._index","parentId":"routes/($locale).account","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._index-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).account.$":{"id":"routes/($locale).account.$","parentId":"routes/($locale).account","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).contact":{"id":"routes/($locale).contact","parentId":"routes/($locale)","path":"contact","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).contact-BY47bZRY.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).rewards":{"id":"routes/($locale).rewards","parentId":"routes/($locale)","path":"rewards","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).rewards-DtR2lrLX.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).search":{"id":"routes/($locale).search","parentId":"routes/($locale)","path":"search","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).search-DTC6gGfj.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-BYzZDKcn.js","/assets/search-DOeYwaXi.js","/assets/Image-83A1PdZQ.js","/assets/Money-St0DOtgu.js"],"css":[]},"routes/($locale)._index":{"id":"routes/($locale)._index","parentId":"routes/($locale)","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._index-DunUea2H.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/index-C2LW_5Ip.js","/assets/OptimizedVideo-Cwhh0DV1.js"],"css":[]},"routes/($locale).cart":{"id":"routes/($locale).cart","parentId":"routes/($locale)","path":"cart","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart-CJkeVtRU.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/jsx-runtime-CWqDQG74.js","/assets/chunk-D4RADZKF-CZTShXQu.js","/assets/CartMain-De5XrJ0a.js","/assets/index-BYzZDKcn.js","/assets/Aside-D-IBZjP3.js","/assets/variants-QTzM9WEo.js","/assets/ProductPrice-DA7E5Y22.js","/assets/Money-St0DOtgu.js","/assets/Image-83A1PdZQ.js"],"css":[]},"routes/($locale).cart.$lines":{"id":"routes/($locale).cart.$lines","parentId":"routes/($locale).cart","path":":lines","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart._lines-CeW0WZU7.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]},"routes/($locale).$":{"id":"routes/($locale).$","parentId":"routes/($locale)","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._-D4yvO5ou.js","imports":["/assets/with-props-CE_bzIRz.js","/assets/chunk-D4RADZKF-CZTShXQu.js"],"css":[]}},"url":"/assets/manifest-caa334cd.js","version":"caa334cd"};