import {useNavigate, useSearchParams} from 'react-router';

// Coffee filters
const ROAST_LEVELS = [
  {label: 'All Roasts', value: 'all'},
  {label: 'Light Roast', value: 'light'},
  {label: 'Medium Roast', value: 'medium'},
  {label: 'Dark Roast', value: 'dark'},
];

// K-Cup filters
const KCUP_TYPES = [
  {label: 'All K-Cups', value: 'all'},
  {label: 'Single Origin', value: 'single-origin'},
  {label: 'Blends', value: 'blend'},
  {label: 'Flavored', value: 'flavored'},
];

// Gear filters
const GEAR_CATEGORIES = [
  {label: 'All Gear', value: 'all'},
  {label: 'Mugs & Cups', value: 'mugs'},
  {label: 'Equipment', value: 'equipment'},
  {label: 'Apparel', value: 'apparel'},
  {label: 'Accessories', value: 'accessories'},
];

const PRICE_RANGES = [
  {label: 'All Prices', value: 'all'},
  {label: 'Under $25', value: 'under-25'},
  {label: '$25 - $50', value: '25-50'},
  {label: 'Over $50', value: 'over-50'},
];

export function ProductFilters({
  isOpen = true,
  onToggle,
  activeSection = 'coffee'
}: {
  isOpen?: boolean;
  onToggle?: () => void;
  activeSection?: string;
}) {
  // Only show filters for coffee section
  if (activeSection !== 'coffee') {
    return null;
  }
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Get filter values from URL params (coffee only)
  const roast = searchParams.get('roast') || 'all';
  const priceRange = searchParams.get('price') || 'all';

  // Function to update filters in URL
  function updateFilter(key: string, value: string) {
    const newParams = new URLSearchParams(searchParams);

    if (value && value !== 'all') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }

    navigate(`?${newParams.toString()}`);
  }

  // Clear all filters
  function clearAllFilters() {
    const newParams = new URLSearchParams();
    // Keep section parameter but clear all filters
    const section = searchParams.get('section');
    if (section) {
      newParams.set('section', section);
    }
    navigate(`?${newParams.toString()}`);
  }

  const hasActiveFilters = roast !== 'all' || priceRange !== 'all';



  return (
    <div className="bg-amber-600 rounded-xl shadow-sm border border-amber-500 overflow-hidden">
      {/* Filter header */}
      <div className="flex items-center justify-between p-6 bg-amber-700 border-b border-amber-500">
        <div className="flex items-center">
          <svg className="w-5 h-5 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          <h2 className="text-lg font-bold text-white">Filters</h2>
        </div>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-white hover:text-gray-200 font-medium transition-colors"
            >
              Clear All
            </button>
          )}
          {onToggle && (
            <button
              onClick={onToggle}
              className="lg:hidden p-1 text-gray-200 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Filter Content */}
      <div className={`transition-all duration-300 ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="p-6 space-y-8">

          {/* Coffee Roast Level Filter */}
          <div>
            <h3 className="text-sm font-semibold text-white mb-4 flex items-center">
              <svg className="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 716.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
              </svg>
              Roast Level
            </h3>
            <div className="space-y-3">
              {ROAST_LEVELS.map((item) => (
                <label
                  key={item.value}
                  className="flex items-center cursor-pointer group"
                >
                  <input
                    type="radio"
                    name="roast"
                    checked={roast === item.value}
                    onChange={() => updateFilter('roast', item.value)}
                    className="h-4 w-4 text-army-600 border-amber-400 bg-amber-500 focus:ring-army-500 focus:ring-offset-amber-600"
                  />
                  <span className="ml-3 text-sm text-white group-hover:text-gray-200 transition-colors">
                    {item.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div>
            <h3 className="text-sm font-semibold text-white mb-4 flex items-center">
              <svg className="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              Price Range
            </h3>
            <div className="space-y-3">
              {PRICE_RANGES.map((price) => (
                <label
                  key={price.value}
                  className="flex items-center cursor-pointer group"
                >
                  <input
                    type="radio"
                    name="price-range"
                    checked={priceRange === price.value}
                    onChange={() => updateFilter('price', price.value)}
                    className="h-4 w-4 text-army-600 border-amber-400 bg-amber-500 focus:ring-army-500 focus:ring-offset-amber-600"
                  />
                  <span className="ml-3 text-sm text-white group-hover:text-gray-200 transition-colors">
                    {price.label}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
