/* Homepage-specific styles to prevent hydration mismatches */
body.homepage {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden;
}

body.homepage header:not(.show-on-scroll) {
  opacity: 0 !important;
  transform: translateY(-100%) !important;
  transition: opacity 0.3s ease, transform 0.3s ease !important;
  pointer-events: none !important;
}

body.homepage header.show-on-scroll {
  opacity: 1 !important;
  transform: translateY(0) !important;
  pointer-events: auto !important;
}

body.homepage main {
  padding: 0 !important;
  margin: 0 !important;
}

body.homepage footer {
  display: none !important;
}

/* Override the header's existing transform classes on homepage */
body.homepage header.translate-y-0 {
  transform: translateY(-100%) !important;
}

body.homepage header.show-on-scroll.translate-y-0 {
  transform: translateY(0) !important;
}
