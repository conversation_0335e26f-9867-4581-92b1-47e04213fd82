import{j as t}from"./jsx-runtime-CWqDQG74.js";import{r}from"./chunk-D4RADZKF-CZTShXQu.js";function H(){const[j,E]=r.useState(!1);return r.useEffect(()=>{const l=()=>{const d=window.innerWidth,f=navigator.userAgent.toLowerCase(),w=["mobile","android","iphone","ipad","tablet"].some(x=>f.includes(x));E(d<=768||w)};return l(),window.addEventListener("resize",l),()=>window.removeEventListener("resize",l)},[]),j}function q({src:j,fallbackImage:E,poster:l,className:d="",style:f={},autoPlay:c=!0,muted:w=!0,loop:x=!0,playsInline:S=!0,preload:A="metadata",lazy:p=!0,forceMobileImage:F=!0,onLoadStart:g,onCanPlay:m,onError:b}){const s=r.useRef(null),[v,N]=r.useState(!p),[u,y]=r.useState(!1),[R,T]=r.useState(!1),[L,h]=r.useState(!1),[K,k]=r.useState(!1),i=!H()||!F,o=l||E;return r.useEffect(()=>{if(!p||v||!i)return;const e=new IntersectionObserver(n=>{n.forEach(a=>{a.isIntersecting&&(N(!0),e.disconnect())})},{rootMargin:"50px",threshold:.1});return s.current&&e.observe(s.current),()=>e.disconnect()},[p,v,i]),r.useEffect(()=>{const e=s.current;if(!e||!i)return;const n=()=>{g==null||g()},a=()=>{y(!0),m==null||m()},I=()=>{k(!0)},M=()=>{y(!0)},V=()=>{T(!0),b==null||b()},$=()=>{h(!0)},O=()=>{h(!1)};return e.addEventListener("loadstart",n),e.addEventListener("canplay",a),e.addEventListener("canplaythrough",I),e.addEventListener("loadeddata",M),e.addEventListener("error",V),e.addEventListener("play",$),e.addEventListener("pause",O),()=>{e.removeEventListener("loadstart",n),e.removeEventListener("canplay",a),e.removeEventListener("canplaythrough",I),e.removeEventListener("loadeddata",M),e.removeEventListener("error",V),e.removeEventListener("play",$),e.removeEventListener("pause",O)}},[g,m,b,i]),r.useEffect(()=>{if(u&&c&&s.current&&!L&&i){const e=s.current.play();e!==void 0&&e.catch(n=>{console.warn("Video autoplay failed:",n),h(!1)})}},[u,c,L,i]),r.useEffect(()=>{if(!p&&s.current&&v&&i){const e=s.current;if(e.load(),c){const n=e.play();n!==void 0&&n.catch(a=>{console.warn("Initial video autoplay failed:",a)})}}},[p,c,v,i]),r.useEffect(()=>{if(!s.current||!i)return;const e=new IntersectionObserver(n=>{n.forEach(a=>{s.current&&(a.isIntersecting?c&&u&&(s.current.play().catch(()=>{}),h(!0)):(s.current.pause(),h(!1)))})},{threshold:.3,rootMargin:"50px"});return e.observe(s.current),()=>e.disconnect()},[c,u,i]),R&&o?t.jsx("img",{src:o,alt:"Video fallback",className:d,style:f}):i?t.jsxs("div",{className:"relative",style:f,children:[!u&&o&&t.jsxs("picture",{children:[t.jsx("source",{srcSet:o.replace(/\.(png|jpg|jpeg)$/i,".webp"),type:"image/webp"}),t.jsx("img",{src:o,alt:"Loading...",className:`${d} absolute inset-0 w-full h-full object-cover z-10`,style:{objectFit:"cover"},loading:"eager"})]}),v&&t.jsxs("video",{ref:s,className:`${d} ${u?"opacity-100":"opacity-0"} transition-opacity duration-500 ease-in-out`,autoPlay:c,muted:w,loop:x,playsInline:S,preload:A,poster:l,style:{objectFit:"cover"},children:[t.jsx("source",{src:j,type:"video/mp4"}),o&&t.jsx("img",{src:o,alt:"Video not supported",className:"w-full h-full object-cover"})]})]}):t.jsx("div",{className:"relative",style:f,children:o?t.jsxs("picture",{children:[t.jsx("source",{srcSet:o.replace(/\.(png|jpg|jpeg)$/i,".webp"),type:"image/webp"}),t.jsx("img",{src:o,alt:"Video poster",className:d,style:{objectFit:"cover"},loading:"eager"})]}):t.jsx("div",{className:`${d} bg-gray-200 flex items-center justify-center`,children:t.jsx("span",{className:"text-gray-500",children:"Video not available"})})})}export{q as O};
