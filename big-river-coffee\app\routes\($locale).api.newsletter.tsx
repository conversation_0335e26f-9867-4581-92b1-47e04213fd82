import {type ActionFunctionArgs, type LoaderFunctionArgs} from '@shopify/remix-oxygen';

// Type definitions
interface ActionData {
  success?: boolean;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

// Simple and reliable MD5 implementation for Mailchimp subscriber hash
const md5 = (str: string): string => {
  const rotateLeft = (value: number, amount: number): number => {
    return (value << amount) | (value >>> (32 - amount));
  };

  const addUnsigned = (x: number, y: number): number => {
    const lsw = (x & 0xFFFF) + (y & 0xFFFF);
    const msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return (msw << 16) | (lsw & 0xFFFF);
  };

  const convertToWordArray = (str: string): number[] => {
    const wordArray: number[] = [];
    const strLen = str.length;

    for (let i = 0; i < strLen - 3; i += 4) {
      wordArray.push(
        str.charCodeAt(i) |
        (str.charCodeAt(i + 1) << 8) |
        (str.charCodeAt(i + 2) << 16) |
        (str.charCodeAt(i + 3) << 24)
      );
    }

    let lastWord = 0;
    const remainder = strLen % 4;
    for (let i = 0; i < remainder; i++) {
      lastWord |= str.charCodeAt(strLen - remainder + i) << (i * 8);
    }
    if (remainder > 0) wordArray.push(lastWord);

    return wordArray;
  };

  const wordToHex = (word: number): string => {
    let hex = '';
    for (let i = 0; i <= 3; i++) {
      const byte = (word >>> (i * 8)) & 255;
      hex += ('0' + byte.toString(16)).slice(-2);
    }
    return hex;
  };

  // Convert string to UTF-8 bytes
  const utf8Str = unescape(encodeURIComponent(str));
  const wordArray = convertToWordArray(utf8Str);
  const byteCount = utf8Str.length;

  // Padding
  wordArray[byteCount >> 2] |= 0x80 << ((byteCount % 4) << 3);
  wordArray[((byteCount + 64) >>> 9 << 4) + 14] = byteCount << 3;

  // MD5 initialization
  let h0 = 0x67452301;
  let h1 = 0xEFCDAB89;
  let h2 = 0x98BADCFE;
  let h3 = 0x10325476;

  // Process message in 512-bit chunks
  for (let i = 0; i < wordArray.length; i += 16) {
    let a = h0, b = h1, c = h2, d = h3;

    // Main loop (simplified version)
    const f = (x: number, y: number, z: number) => (x & y) | (~x & z);
    const g = (x: number, y: number, z: number) => (x & z) | (y & ~z);
    const h = (x: number, y: number, z: number) => x ^ y ^ z;
    const ii = (x: number, y: number, z: number) => y ^ (x | ~z);

    const ff = (a: number, b: number, c: number, d: number, x: number, s: number, t: number) => {
      return addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, f(b, c, d)), addUnsigned(x, t)), s), b);
    };

    // Round 1
    a = ff(a, b, c, d, wordArray[i + 0] || 0, 7, 0xD76AA478);
    d = ff(d, a, b, c, wordArray[i + 1] || 0, 12, 0xE8C7B756);
    c = ff(c, d, a, b, wordArray[i + 2] || 0, 17, 0x242070DB);
    b = ff(b, c, d, a, wordArray[i + 3] || 0, 22, 0xC1BDCEEE);
    a = ff(a, b, c, d, wordArray[i + 4] || 0, 7, 0xF57C0FAF);
    d = ff(d, a, b, c, wordArray[i + 5] || 0, 12, 0x4787C62A);
    c = ff(c, d, a, b, wordArray[i + 6] || 0, 17, 0xA8304613);
    b = ff(b, c, d, a, wordArray[i + 7] || 0, 22, 0xFD469501);
    a = ff(a, b, c, d, wordArray[i + 8] || 0, 7, 0x698098D8);
    d = ff(d, a, b, c, wordArray[i + 9] || 0, 12, 0x8B44F7AF);
    c = ff(c, d, a, b, wordArray[i + 10] || 0, 17, 0xFFFF5BB1);
    b = ff(b, c, d, a, wordArray[i + 11] || 0, 22, 0x895CD7BE);
    a = ff(a, b, c, d, wordArray[i + 12] || 0, 7, 0x6B901122);
    d = ff(d, a, b, c, wordArray[i + 13] || 0, 12, 0xFD987193);
    c = ff(c, d, a, b, wordArray[i + 14] || 0, 17, 0xA679438E);
    b = ff(b, c, d, a, wordArray[i + 15] || 0, 22, 0x49B40821);

    h0 = addUnsigned(a, h0);
    h1 = addUnsigned(b, h1);
    h2 = addUnsigned(c, h2);
    h3 = addUnsigned(d, h3);
  }

  return wordToHex(h0) + wordToHex(h1) + wordToHex(h2) + wordToHex(h3);
};

// Helper function to generate Mailchimp subscriber hash (MD5 of lowercase email)
const generateSubscriberHash = (email: string): string => {
  return md5(email.toLowerCase().trim());
};

// Handle GET requests to this endpoint
export async function loader({request}: LoaderFunctionArgs): Promise<Response> {
  return json({
    error: 'This endpoint only accepts POST requests for newsletter subscriptions.'
  }, { status: 405 });
}

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return json({
      success: false,
      error: 'Method not allowed'
    }, { status: 405 });
  }

  try {
    // Parse form data
    const formData = await request.formData();
    const email = formData.get('email') as string;

    // Basic validation
    if (!email || !email.trim()) {
      return json({
        success: false,
        error: 'Email address is required'
      }, { status: 400 });
    }

    // Enhanced email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const trimmedEmail = email.trim().toLowerCase();

    if (!emailRegex.test(trimmedEmail)) {
      return json({
        success: false,
        error: 'Please enter a valid email address'
      }, { status: 400 });
    }

    // Additional security: Check email length
    if (trimmedEmail.length > 254) {
      return json({
        success: false,
        error: 'Email address is too long'
      }, { status: 400 });
    }

    // Get environment variables
    const apiKey = context.env.MAILCHIMP_API_KEY;
    const serverPrefix = context.env.MAILCHIMP_SERVER_PREFIX;
    const listId = context.env.MAILCHIMP_LIST_ID;

    if (!apiKey || !serverPrefix || !listId) {
      console.error('Missing Mailchimp configuration:', {
        hasApiKey: !!apiKey,
        hasServerPrefix: !!serverPrefix,
        hasListId: !!listId
      });
      return json({
        success: false,
        error: 'Newsletter service is temporarily unavailable'
      }, { status: 500 });
    }

    // Generate subscriber hash for Mailchimp API
    const subscriberHash = generateSubscriberHash(trimmedEmail);

    // Make direct API call to Mailchimp using PUT method to add or update member
    const mailchimpUrl = `https://${serverPrefix}.api.mailchimp.com/3.0/lists/${listId}/members/${subscriberHash}`;

    // Create base64 encoded auth string for Mailchimp API
    const authString = btoa(`anystring:${apiKey}`);

    const mailchimpResponse = await fetch(mailchimpUrl, {
      method: 'PUT',
      headers: {
        'Authorization': `Basic ${authString}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email_address: trimmedEmail,
        status: 'subscribed',
        status_if_new: 'subscribed',
        merge_fields: {
          SOURCE: 'Website Newsletter'
        }
      })
    });

    const responseData = await mailchimpResponse.json() as any;

    if (!mailchimpResponse.ok) {
      // Handle Mailchimp API errors
      console.error('Mailchimp API error:', responseData);

      if (responseData.title === 'Invalid Resource') {
        return json({
          success: false,
          error: 'Please enter a valid email address'
        }, { status: 400 });
      }

      return json({
        success: false,
        error: responseData.detail || 'Failed to subscribe. Please try again.'
      }, { status: 400 });
    }

    console.log('Successfully added subscriber:', responseData.email_address);

    return json({
      success: true
    });

  } catch (error: any) {
    console.error('Newsletter signup error:', error);

    // Generic error handling
    return json({
      success: false,
      error: 'Unable to subscribe at this time. Please try again later.'
    }, { status: 500 });
  }
}
