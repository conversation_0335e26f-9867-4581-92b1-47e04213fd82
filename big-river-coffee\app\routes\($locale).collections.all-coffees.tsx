import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';

export async function loader({request}: LoaderFunctionArgs) {
  // Redirect /collections/all-coffees to /collections/all
  const url = new URL(request.url);
  const searchParams = url.searchParams.toString();
  const redirectUrl = `/collections/all${searchParams ? `?${searchParams}` : ''}`;
  
  return redirect(redirectUrl, 301);
}

export default function AllCoffeesRedirect() {
  return null;
}
