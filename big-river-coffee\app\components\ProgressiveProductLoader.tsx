import { useState, useEffect } from 'react';
import { useFetcher } from 'react-router';
import { ProductCard } from '~/components/ProductCard';
import type { CollectionItemFragment } from 'storefrontapi.generated';

interface ProgressiveProductLoaderProps {
  initialProducts: CollectionItemFragment[];
  hasNextPage: boolean;
  endCursor?: string;
  sectionName: string;
  queryFilter: string;
}

/**
 * Progressive product loader for collections page
 * Loads additional products on demand for better initial performance
 */
export function ProgressiveProductLoader({
  initialProducts,
  hasNextPage,
  endCursor,
  sectionName,
  queryFilter,
}: ProgressiveProductLoaderProps) {
  const [products, setProducts] = useState(initialProducts);
  const [isLoading, setIsLoading] = useState(false);
  const [currentCursor, setCurrentCursor] = useState(endCursor);
  const [hasMore, setHasMore] = useState(hasNextPage);
  const fetcher = useFetcher();

  const loadMoreProducts = async () => {
    if (isLoading || !hasMore || !currentCursor) return;

    setIsLoading(true);

    // Use fetcher to load more products
    fetcher.load(`/api/products?cursor=${currentCursor}&query=${encodeURIComponent(queryFilter)}&pageBy=8`);
  };

  // Handle fetcher response
  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      const newProducts = fetcher.data.products?.nodes || [];
      const pageInfo = fetcher.data.products?.pageInfo;

      if (newProducts.length > 0) {
        setProducts(prev => [...prev, ...newProducts]);
        setCurrentCursor(pageInfo?.endCursor);
        setHasMore(pageInfo?.hasNextPage || false);
      } else {
        setHasMore(false);
      }
      setIsLoading(false);
    }
  }, [fetcher.data, fetcher.state]);

  // Intersection Observer for auto-loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            loadMoreProducts();
          }
        });
      },
      {
        rootMargin: '200px', // Start loading 200px before reaching the end
        threshold: 0.1,
      }
    );

    const loadMoreTrigger = document.getElementById(`load-more-${sectionName}`);
    if (loadMoreTrigger) {
      observer.observe(loadMoreTrigger);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoading, sectionName]);

  return (
    <>
      {/* Product Grid */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {products.map((product, index) => (
          <ProductCard
            key={product.id}
            product={product}
            loading={index < 6 ? 'eager' : 'lazy'}
            viewMode="grid"
          />
        ))}
      </div>

      {/* Loading Trigger */}
      {hasMore && (
        <div
          id={`load-more-${sectionName}`}
          className="flex justify-center py-8"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-600">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-army-600"></div>
              <span>Loading more products...</span>
            </div>
          ) : (
            <button
              onClick={loadMoreProducts}
              className="px-6 py-3 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium"
            >
              Load More Products
            </button>
          )}
        </div>
      )}

      {/* End Message */}
      {!hasMore && products.length > initialProducts.length && (
        <div className="text-center py-8 text-gray-600">
          <p>You've seen all {products.length} products in this section.</p>
        </div>
      )}
    </>
  );
}

/**
 * Optimized product grid with virtual scrolling for large datasets
 */
interface VirtualizedProductGridProps {
  products: CollectionItemFragment[];
  itemHeight: number;
  containerHeight: number;
  viewMode?: 'grid' | 'list';
}

export function VirtualizedProductGrid({
  products,
  itemHeight = 400,
  containerHeight = 600,
  viewMode = 'grid',
}: VirtualizedProductGridProps) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);

  const itemsPerRow = viewMode === 'grid' ? 3 : 1;
  const totalRows = Math.ceil(products.length / itemsPerRow);
  const visibleRows = Math.ceil(containerHeight / itemHeight);
  const startRow = Math.floor(scrollTop / itemHeight);
  const endRow = Math.min(startRow + visibleRows + 1, totalRows);

  const visibleProducts = products.slice(
    startRow * itemsPerRow,
    endRow * itemsPerRow
  );

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  return (
    <div
      ref={setContainerRef}
      className="overflow-auto"
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalRows * itemHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${startRow * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
              : 'grid-cols-1'
          }`}>
            {visibleProducts.map((product, index) => (
              <ProductCard
                key={product.id}
                product={product}
                loading="lazy"
                viewMode={viewMode}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
