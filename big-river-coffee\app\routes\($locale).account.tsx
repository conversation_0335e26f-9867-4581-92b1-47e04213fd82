import {
  data as remixData,
  redirect,
  type LoaderFunctionArgs,
} from '@shopify/remix-oxygen';
import {Form, NavLink, Outlet, useLoaderData} from 'react-router';
import {CUSTOMER_DETAILS_QUERY} from '~/graphql/customer-account/CustomerDetailsQuery';

export function shouldRevalidate() {
  return true;
}

export async function loader({context, request}: LoaderFunctionArgs) {
  try {
    // First check authentication status
    await context.customerAccount.handleAuthStatus();

    const {data, errors} = await context.customerAccount.query(
      CUSTOMER_DETAILS_QUERY,
    );

    if (errors?.length || !data?.customer) {
      // Instead of throwing an error, redirect to login
      const url = new URL(request.url);
      return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
    }

    return remixData(
      {customer: data.customer},
      {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      },
    );
  } catch (error) {
    // If any authentication error occurs, redirect to login
    const url = new URL(request.url);
    return redirect(`/account/login?returnTo=${encodeURIComponent(url.pathname)}`);
  }
}

export default function AccountLayout() {
  const {customer} = useLoaderData<typeof loader>();

  const heading = customer
    ? customer.firstName
      ? `Welcome, ${customer.firstName}`
      : `Welcome to your account`
    : 'Account Details';

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f97316' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Account Header */}
        <div className="bg-white shadow-sm rounded-lg mb-6 p-6 border border-gray-100">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-gray-900">{heading}</h1>
              <p className="text-sm text-gray-500 mt-1">
                Manage your account details and orders
              </p>
            </div>
            <Logout />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar Navigation */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white shadow-sm rounded-lg p-4 border border-gray-100 sticky top-[calc(var(--header-height)+1rem)]">
              <AccountMenu />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-100">
              <Outlet context={{customer}} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function AccountMenu() {
  return (
    <nav className="flex flex-col space-y-1" role="navigation">
      <NavLink
        to="/account/orders"
        className={({isActive, isPending}) =>
          `px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            isActive
              ? 'bg-army-600 !text-white'
              : isPending
                ? 'bg-gray-100 text-gray-500'
                : 'text-gray-700 hover:bg-gray-50'
          }`
        }
      >
        <div className="flex items-center">
          <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Orders
        </div>
      </NavLink>

      <NavLink
        to="/account/profile"
        className={({isActive, isPending}) =>
          `px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            isActive
              ? 'bg-army-600 !text-white'
              : isPending
                ? 'bg-gray-100 text-gray-500'
                : 'text-gray-700 hover:bg-gray-50'
          }`
        }
      >
        <div className="flex items-center">
          <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          Profile
        </div>
      </NavLink>

      <a
        href="https://bigriverbe.myshopify.com/a/subscriptions/login"
        target="_blank"
        rel="noopener noreferrer"
        className="px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-700 hover:bg-gray-50 flex items-center"
      >
        <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Manage Subscriptions
        <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
      </a>

      <NavLink
        to="/account/addresses"
        className={({isActive, isPending}) =>
          `px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            isActive
              ? 'bg-army-600 !text-white'
              : isPending
                ? 'bg-gray-100 text-gray-500'
                : 'text-gray-700 hover:bg-gray-50'
          }`
        }
      >
        <div className="flex items-center">
          <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Addresses
        </div>
      </NavLink>
    </nav>
  );
}

function Logout() {
  return (
    <Form method="POST" action="/account/logout">
      <button
        type="submit"
        className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-army-600 transition-colors"
      >
        <svg className="mr-2 -ml-1 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
        </svg>
        Sign out
      </button>
    </Form>
  );
}
