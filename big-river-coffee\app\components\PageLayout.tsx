import {Await, Link, useLocation} from 'react-router';
import {Suspense, useId, useEffect, useState} from 'react';
import type {
  CartApiQueryFragment,
  FooterQuery,
  HeaderQuery,
} from 'storefrontapi.generated';
import {Aside} from '~/components/Aside';
import {Footer} from '~/components/Footer';
import {Header, HeaderMenu} from '~/components/Header';
import {CartMain} from '~/components/CartMain';
import {
  SEARCH_ENDPOINT,
  SearchFormPredictive,
} from '~/components/SearchFormPredictive';
import {SearchResultsPredictive} from '~/components/SearchResultsPredictive';
import PromoPopup from '~/components/PromoPopup';
import {TrafficSourceTracker} from '~/components/TrafficSourceTracker';
import {PerformanceOptimizations} from '~/components/PerformanceOptimizations';

interface PageLayoutProps {
  cart: Promise<CartApiQueryFragment | null>;
  footer: Promise<FooterQuery | null>;
  header: Header<PERSON>uery;
  isLoggedIn: Promise<boolean>;
  publicStoreDomain: string;
  children?: React.ReactNode;
}

function PageTransition({children}: {children: React.ReactNode}) {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(true); // Start visible to prevent flash

  useEffect(() => {
    // Only apply transition on route changes, not initial load
    if (location.pathname) {
      setIsVisible(false);
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    }
  }, [location.pathname]);

  return (
    <div
      className={`transition-all duration-300 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
      }`}
      style={{ minHeight: '200px' }} // Prevent layout shift
    >
      {children}
    </div>
  );
}

export function PageLayout({
  cart,
  children = null,
  footer,
  header,
  isLoggedIn,
  publicStoreDomain,
}: PageLayoutProps) {
  // POPUP CONTROL: Set to false to disable popup completely
  const POPUP_ENABLED = false;

  const [showPromoPopup, setShowPromoPopup] = useState(false);

  useEffect(() => {
    // Skip popup logic if disabled
    if (!POPUP_ENABLED) return;

    // Check if popup has been shown before
    const hasSeenPopup = localStorage.getItem('bigriver-promo-popup-seen');

    // For testing: uncomment the line below to always show popup
    // localStorage.removeItem('bigriver-promo-popup-seen');

    if (!hasSeenPopup) {
      // Show popup after a short delay
      const timer = setTimeout(() => {
        setShowPromoPopup(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [POPUP_ENABLED]);

  const handleClosePopup = () => {
    setShowPromoPopup(false);
    // Mark popup as seen so it doesn't show again
    localStorage.setItem('bigriver-promo-popup-seen', 'true');
  };

  return (
    <Aside.Provider>
      <CartAside cart={cart} />
      <SearchAside />
      <MobileMenuAside header={header} publicStoreDomain={publicStoreDomain} />
      {header && (
        <Header
          header={header}
          cart={cart}
          isLoggedIn={isLoggedIn}
          publicStoreDomain={publicStoreDomain}
        />
      )}
      <main>
        <PageTransition>{children}</PageTransition>
      </main>
      <Footer
        footer={footer}
        header={header}
        publicStoreDomain={publicStoreDomain}
      />

      {/* Traffic Source Tracking */}
      <TrafficSourceTracker />

      {/* Performance Optimizations */}
      <PerformanceOptimizations />

      {/* Promo Popup */}
      <PromoPopup isOpen={showPromoPopup} onClose={handleClosePopup} />
    </Aside.Provider>
  );
}

function CartAside({cart}: {cart: PageLayoutProps['cart']}) {
  return (
    <Aside type="cart" heading="Shopping Cart">
      <Suspense fallback={
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"></div>
          <span className="ml-3 text-army-600 font-medium">Loading cart...</span>
        </div>
      }>
        <Await resolve={cart}>
          {(cart) => {
            return <CartMain cart={cart} layout="aside" />;
          }}
        </Await>
      </Suspense>
    </Aside>
  );
}

function SearchAside() {
  const queriesDatalistId = useId();
  return (
    <Aside type="search" heading="SEARCH">
      <div className="h-full flex flex-col space-y-6">
        {/* Search Input - Fixed at top */}
        <div className="flex-shrink-0">
          <SearchFormPredictive>
            {({fetchResults, goToSearch, inputRef}) => (
              <div className="space-y-4">
                <div className="relative">
                  <input
                    name="q"
                    onChange={fetchResults}
                    onFocus={fetchResults}
                    placeholder="Search for coffee, gear, or anything..."
                    ref={inputRef}
                    type="search"
                    list={queriesDatalistId}
                    className="w-full px-4 py-3 bg-white border border-army-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 text-gray-900 placeholder-gray-500"
                  />
                </div>

                <button
                  onClick={goToSearch}
                  className="w-full bg-army-600 text-white py-3 px-4 rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium"
                >
                  Search All Results
                </button>
              </div>
            )}
          </SearchFormPredictive>
        </div>

        {/* Search Results - Scrollable */}
        <div className="flex-1 overflow-y-auto">
          <SearchResultsPredictive>
            {({items, total, term, state, closeSearch}) => {
              const {articles, collections, pages, products, queries} = items;

              if (state === 'loading' && term.current) {
                return (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"></div>
                    <span className="ml-3 text-army-600 font-medium">Searching...</span>
                  </div>
                );
              }

              if (!total) {
                return <SearchResultsPredictive.Empty term={term} />;
              }

              return (
                <div className="space-y-6">
                  <SearchResultsPredictive.Queries
                    queries={queries}
                    queriesDatalistId={queriesDatalistId}
                  />
                  <SearchResultsPredictive.Products
                    products={products}
                    closeSearch={closeSearch}
                    term={term}
                  />
                  <SearchResultsPredictive.Collections
                    collections={collections}
                    closeSearch={closeSearch}
                    term={term}
                  />
                  <SearchResultsPredictive.Pages
                    pages={pages}
                    closeSearch={closeSearch}
                    term={term}
                  />
                  <SearchResultsPredictive.Articles
                    articles={articles}
                    closeSearch={closeSearch}
                    term={term}
                  />
                  {term.current && total ? (
                    <div className="pt-4 border-t border-army-200">
                      <Link
                        onClick={closeSearch}
                        to={`${SEARCH_ENDPOINT}?q=${term.current}`}
                        className="block w-full p-4 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 text-center font-medium"
                      >
                        View all {total} results for "{term.current}"
                      </Link>
                    </div>
                  ) : null}
                </div>
              );
            }}
          </SearchResultsPredictive>
        </div>
      </div>
    </Aside>
  );
}

function MobileMenuAside({
  header,
  publicStoreDomain,
}: {
  header: PageLayoutProps['header'];
  publicStoreDomain: PageLayoutProps['publicStoreDomain'];
}) {
  return (
    header.menu &&
    header.shop.primaryDomain?.url && (
      <Aside type="mobile" heading="MENU">
        <HeaderMenu
          menu={header.menu}
          viewport="mobile"
          primaryDomainUrl={header.shop.primaryDomain.url}
          publicStoreDomain={publicStoreDomain}
        />
      </Aside>
    )
  );
}
