import{G as S}from"./index-De_rdhZz.js";import{r as G}from"./chunk-D4RADZKF-CZTShXQu.js";import{u as M,f as j}from"./root-FWd9Q6Ql.js";import"./jsx-runtime-CWqDQG74.js";import"./with-props-CE_bzIRz.js";import"./Aside-D-IBZjP3.js";import"./CartMain-ComcczCM.js";import"./variants-QTzM9WEo.js";import"./ProductPrice-DA7E5Y22.js";import"./Money-St0DOtgu.js";import"./Image-83A1PdZQ.js";import"./search-DOeYwaXi.js";function N(){const{subscribe:v,register:D}=S(),[C,U]=G.useState(!1),{utmParams:r,hasUTM:A}=M();return G.useEffect(()=>{const e=setTimeout(()=>{if(typeof window.gtag=="function"){U(!0);return}const a=document.createElement("script");a.async=!0,a.src="https://www.googletagmanager.com/gtag/js?id=G-KWTBMRWDGP",a.onload=()=>{window.dataLayer=window.dataLayer||[];function i(...o){window.dataLayer.push(o)}window.gtag=i,i("js",new Date),i("config","G-KWTBMRWDGP",{send_page_view:!0,allow_google_signals:!0,allow_ad_personalization_signals:!0,custom_map:{custom_parameter_1:"coffee_type",custom_parameter_2:"subscription_type"}}),U(!0),console.log("Google Analytics loaded successfully for Big River Coffee")},a.onerror=()=>{console.error("Failed to load Google Analytics")},document.head.appendChild(a)},100);return()=>clearTimeout(e)},[]),G.useEffect(()=>{C&&(D("Google Analytics 4"),v("page_viewed",t=>{if(typeof window<"u"&&window.gtag){const e={page_title:t.pageTitle||document.title,page_location:t.url||window.location.href,page_path:t.path||window.location.pathname};A&&(e.campaign_source=r.utm_source,e.campaign_medium=r.utm_medium,e.campaign_name=r.utm_campaign,e.campaign_content=r.utm_content,e.campaign_term=r.utm_term,e.campaign_id=r.utm_id,e.traffic_source=r.utm_source,e.traffic_medium=r.utm_medium,e.traffic_campaign=r.utm_campaign),window.gtag("event","page_view",e),A&&console.log("🎯 Traffic Source Tracked:",{source:r.utm_source,medium:r.utm_medium,campaign:r.utm_campaign,full_data:e})}}),v("product_viewed",t=>{var e;if(typeof window<"u"&&window.gtag){const i=(t.products||[])[0];if(i){const o=typeof i.price=="object"?parseFloat(((e=i.price)==null?void 0:e.amount)||"0"):parseFloat(i.price||"0");window.gtag("event","view_item",{currency:t.currency||"USD",value:o,items:[{item_id:i.id,item_name:i.title,item_category:i.productType||"Coffee",item_variant:t.variantTitle,price:o,quantity:1}]})}}}),v("product_added_to_cart",t=>{var e,a;if(typeof window<"u"&&window.gtag){const o=(t.products||[])[0];if(o){const m=typeof o.price=="object"?parseFloat(((e=o.price)==null?void 0:e.amount)||"0"):parseFloat(o.price||"0"),s=t.quantity||1,u={currency:typeof o.price=="object"&&((a=o.price)==null?void 0:a.currencyCode)||"USD",value:m*s,items:[{item_id:o.id,item_name:o.title,item_category:o.productType||"Coffee",item_variant:t.variantTitle,price:m,quantity:s}]};A&&(u.traffic_source=r.utm_source,u.traffic_medium=r.utm_medium,u.traffic_campaign=r.utm_campaign),window.gtag("event","add_to_cart",u)}}}),v("cart_viewed",t=>{var e,a,i,o,m,s,u,p;if(typeof window<"u"&&window.gtag){const T=(((a=(e=t.cart)==null?void 0:e.lines)==null?void 0:a.nodes)||[]).map(c=>{var _,h,g,n,d,f,l,w,y;return{item_id:(h=(_=c.merchandise)==null?void 0:_.product)==null?void 0:h.id,item_name:(n=(g=c.merchandise)==null?void 0:g.product)==null?void 0:n.title,item_category:((f=(d=c.merchandise)==null?void 0:d.product)==null?void 0:f.productType)||"Coffee",item_variant:(l=c.merchandise)==null?void 0:l.title,price:parseFloat(((y=(w=c.merchandise)==null?void 0:w.price)==null?void 0:y.amount)||"0"),quantity:c.quantity||1}});window.gtag("event","view_cart",{currency:((m=(o=(i=t.cart)==null?void 0:i.cost)==null?void 0:o.totalAmount)==null?void 0:m.currencyCode)||"USD",value:parseFloat(((p=(u=(s=t.cart)==null?void 0:s.cost)==null?void 0:u.totalAmount)==null?void 0:p.amount)||"0"),items:T})}}),v("custom_checkout_started",t=>{var e,a,i,o,m,s,u,p;if(typeof window<"u"&&window.gtag){const T=(((a=(e=t.cart)==null?void 0:e.lines)==null?void 0:a.nodes)||[]).map(c=>{var _,h,g,n,d,f,l,w,y;return{item_id:(h=(_=c.merchandise)==null?void 0:_.product)==null?void 0:h.id,item_name:(n=(g=c.merchandise)==null?void 0:g.product)==null?void 0:n.title,item_category:((f=(d=c.merchandise)==null?void 0:d.product)==null?void 0:f.productType)||"Coffee",item_variant:(l=c.merchandise)==null?void 0:l.title,price:parseFloat(((y=(w=c.merchandise)==null?void 0:w.price)==null?void 0:y.amount)||"0"),quantity:c.quantity||1}});window.gtag("event","begin_checkout",{currency:((m=(o=(i=t.cart)==null?void 0:i.cost)==null?void 0:o.totalAmount)==null?void 0:m.currencyCode)||"USD",value:parseFloat(((p=(u=(s=t.cart)==null?void 0:s.cost)==null?void 0:u.totalAmount)==null?void 0:p.amount)||"0"),items:T})}}),v("custom_purchase",t=>{var e,a,i,o,m,s,u,p,F,T,c,_;if(typeof window<"u"&&window.gtag){const h=((a=(e=t.order)==null?void 0:e.lineItems)==null?void 0:a.map(n=>{var d,f,l,w,y,b,q,L,P;return{item_id:(f=(d=n.variant)==null?void 0:d.product)==null?void 0:f.id,item_name:(w=(l=n.variant)==null?void 0:l.product)==null?void 0:w.title,item_category:((b=(y=n.variant)==null?void 0:y.product)==null?void 0:b.productType)||"Coffee",item_variant:(q=n.variant)==null?void 0:q.title,price:parseFloat(((P=(L=n.variant)==null?void 0:L.price)==null?void 0:P.amount)||"0"),quantity:n.quantity||1}}))||[],g=A?j(r):{};window.gtag("event","purchase",{transaction_id:(i=t.order)==null?void 0:i.id,currency:((m=(o=t.order)==null?void 0:o.totalPrice)==null?void 0:m.currencyCode)||"USD",value:parseFloat(((u=(s=t.order)==null?void 0:s.totalPrice)==null?void 0:u.amount)||"0"),items:h,coffee_subscription:((F=(p=t.order)==null?void 0:p.lineItems)==null?void 0:F.some(n=>{var d;return(d=n.sellingPlan)==null?void 0:d.id}))||!1,...g}),A&&console.log("Purchase conversion with UTM attribution:",{transaction_id:(T=t.order)==null?void 0:T.id,value:parseFloat(((_=(c=t.order)==null?void 0:c.totalPrice)==null?void 0:_.amount)||"0"),utm_data:g})}}))},[C,v,D]),null}export{N as GoogleAnalytics};
