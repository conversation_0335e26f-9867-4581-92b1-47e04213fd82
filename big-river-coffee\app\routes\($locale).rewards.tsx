import { type MetaFunction } from 'react-router';
import { useEffect } from 'react';
import {initScrollAnimations} from '~/lib/scrollAnimations';

export const meta: MetaFunction = () => {
  return [
    { title: 'Big River Rewards | Loyalty Program' },
    { description: 'Join our loyalty program and earn points with every purchase, social follow, and adventure shared. Redeem points for discounts and exclusive rewards.' }
  ];
};

export default function RewardsPage() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add body class for page-specific styling
    document.body.classList.add('rewards');

    // Initialize scroll animations
    initScrollAnimations();

    return () => {
      document.body.classList.remove('rewards');
    };
  }, []);

  return (
    <div className="min-h-screen bg-cream">

      {/* Main Content */}
      <div className="py-20 relative overflow-hidden scroll-fade-in delay-200">
        {/* Decorative circles */}
        <div className="absolute top-8 left-8 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>
        <div className="absolute bottom-12 right-12 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
        <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>

        <div className="max-w-6xl mx-auto px-6 relative z-10">
          {/* How It Works */}
          <div className="mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-black text-center mb-12" style={{ fontFamily: 'var(--font-title)' }}>
              How It Works
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-army-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">1</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3" style={{ fontFamily: 'var(--font-header)' }}>Sign Up</h3>
                <p className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>
                  Create an account and start earning points immediately
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-army-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">2</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3" style={{ fontFamily: 'var(--font-header)' }}>Earn Points</h3>
                <p className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>
                  Get 5 points for every $1 spent, plus bonus points for social follows and birthdays
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-army-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">3</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3" style={{ fontFamily: 'var(--font-header)' }}>Redeem Rewards</h3>
                <p className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>
                  Turn your points into discounts: $5 off (500 pts), $10 off (1000 pts), $20 off (2000 pts)
                </p>
              </div>
            </div>
          </div>

          {/* Ways to Earn & Redeem */}
          <div className="grid lg:grid-cols-2 gap-12 mb-12">
            {/* Ways to Earn */}
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <h3 className="text-2xl font-bold text-black mb-6" style={{ fontFamily: 'var(--font-title)' }}>
                Ways to Earn Points
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>Every $1 spent</span>
                  <span className="font-semibold text-army-600">5 points</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>First order</span>
                  <span className="font-semibold text-army-600">300 points</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>Newsletter signup</span>
                  <span className="font-semibold text-army-600">200 points</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>Social media follows</span>
                  <span className="font-semibold text-army-600">125 points</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>Birthday celebration</span>
                  <span className="font-semibold text-army-600">500 points</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-700" style={{ fontFamily: 'var(--font-body)' }}>Daily website visit</span>
                  <span className="font-semibold text-army-600">20 points</span>
                </div>
              </div>
            </div>

            {/* Ways to Redeem */}
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <h3 className="text-2xl font-bold text-black mb-6" style={{ fontFamily: 'var(--font-title)' }}>
                Redeem Your Points
              </h3>
              <div className="space-y-6">
                <div className="bg-army-50 rounded-lg p-4 border border-army-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-lg font-semibold text-army-800">$5 Off</span>
                    <span className="text-army-600 font-bold">500 points</span>
                  </div>
                  <p className="text-sm text-army-700">Perfect for trying a new blend</p>
                </div>
                <div className="bg-army-50 rounded-lg p-4 border border-army-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-lg font-semibold text-army-800">$10 Off</span>
                    <span className="text-army-600 font-bold">1000 points</span>
                  </div>
                  <p className="text-sm text-army-700">Great for your monthly coffee supply</p>
                </div>
                <div className="bg-army-50 rounded-lg p-4 border border-army-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-lg font-semibold text-army-800">$20 Off</span>
                    <span className="text-army-600 font-bold">2000 points</span>
                  </div>
                  <p className="text-sm text-army-700">Stock up on your favorites</p>
                </div>
              </div>
            </div>
          </div>

          {/* Referral Program */}
          <div className="bg-gradient-to-r from-army-600 to-army-700 rounded-xl p-8 text-center text-white mb-12">
            <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'var(--font-title)' }}>
              Refer a Friend, Both Win!
            </h3>
            <p className="text-lg mb-6 opacity-90" style={{ fontFamily: 'var(--font-body)' }}>
              Give your friends 10% off their first order, and you'll get 10% off once they make a purchase.
            </p>
            <a
              href="https://bigriverbe.myshopify.com/pages/loyalty-program"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-white text-army-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              <span>See Rewards</span>
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
