import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router';
import { 
  UTMParameters, 
  getCurrentUTMParameters, 
  extractUTMFromURL, 
  storeUTMParameters 
} from '~/lib/utm';

/**
 * Custom hook for UTM parameter tracking
 * Automatically captures UTM parameters from URL and persists them across navigation
 */
export function useUTMTracking() {
  const [searchParams] = useSearchParams();
  const [utmParams, setUtmParams] = useState<UTMParameters>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Extract UTM parameters from current URL
    const urlUTM = extractUTMFromURL(searchParams);
    
    // Get current UTM parameters (URL takes precedence over stored)
    const currentUTM = getCurrentUTMParameters(searchParams);
    
    setUtmParams(currentUTM);
    setIsLoading(false);
    
    // Log UTM capture for debugging
    if (Object.keys(urlUTM).length > 0) {
      console.log('UTM parameters captured from URL:', urlUTM);
    }
    
    if (Object.keys(currentUTM).length > 0) {
      console.log('Active UTM parameters:', currentUTM);
    }
  }, [searchParams]);

  return {
    utmParams,
    isLoading,
    hasUTM: Object.keys(utmParams).length > 0,
  };
}
