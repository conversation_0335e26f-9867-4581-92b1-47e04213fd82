import{r as d,a as Z,i as X,c as Oe,L as ue,d as Ge,E as He}from"./chunk-D4RADZKF-CZTShXQu.js";import{j as _}from"./jsx-runtime-CWqDQG74.js";const Fe="modulepreload",Je=function(e){return"https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2077600/"+e},le={},Ke=function(t,r,n){let a=Promise.resolve();if(r&&r.length>0){let o=function(u){return Promise.all(u.map(l=>Promise.resolve(l).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),c=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));a=o(r.map(u=>{if(u=Je(u),u in le)return;le[u]=!0;const l=u.endsWith(".css"),f=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const p=document.createElement("link");if(p.rel=l?"stylesheet":Fe,l||(p.as="script"),p.crossOrigin="",p.href=u,c&&p.setAttribute("nonce",c),document.head.appendChild(p),l)return new Promise((y,g)=>{p.addEventListener("load",y),p.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(o){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=o,window.dispatchEvent(s),!s.defaultPrevented)throw o}return a.then(o=>{for(const s of o||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})};function N(e){if(!e){const t=`flattenConnection(): needs a 'connection' to flatten, but received '${e??""}' instead.`;return console.error(t+" Returning an empty array"),[]}return"nodes"in e?e.nodes:"edges"in e&&Array.isArray(e.edges)?e.edges.map(t=>{if(!(t!=null&&t.node))throw new Error("flattenConnection(): Connection edges must contain nodes");return t.node}):[]}const O="_shopify_y",x="_shopify_s";var Ye=new Set(["domain","path","max-age","expires","samesite","secure","httponly"]);function ze(e){let t={},r,n,a=0,i=e.split(/;\s*/g),o,s;for(;a<i.length;a++)if(n=i[a],r=n.indexOf("="),~r){if(o=n.substring(0,r++).trim(),s=n.substring(r).trim(),s[0]==='"'&&(s=s.substring(1,s.length-1)),~s.indexOf("%"))try{s=decodeURIComponent(s)}catch{}Ye.has(n=o.toLowerCase())?n==="expires"?t.expires=new Date(s):n==="max-age"?t.maxage=+s:t[n]=s:t[o]=s}else(o=n.trim().toLowerCase())&&(o==="httponly"||o==="secure")&&(t[o]=!0);return t}function Qe(e,t,r={}){let n=e+"="+encodeURIComponent(t);return r.expires&&(n+="; Expires="+new Date(r.expires).toUTCString()),r.maxage!=null&&r.maxage>=0&&(n+="; Max-Age="+(r.maxage|0)),r.domain&&(n+="; Domain="+r.domain),r.path&&(n+="; Path="+r.path),r.samesite&&(n+="; SameSite="+r.samesite),(r.secure||r.samesite==="None")&&(n+="; Secure"),r.httponly&&(n+="; HttpOnly"),n}const de="xxxx-4xxx-xxxx-xxxxxxxxxxxx";function H(){let e="";try{const t=window.crypto,r=new Uint16Array(31);t.getRandomValues(r);let n=0;e=de.replace(/[x]/g,a=>{const i=r[n]%16,o=a==="x"?i:i&3|8;return n++,o.toString(16)}).toUpperCase()}catch{e=de.replace(/[x]/g,r=>{const n=Math.random()*16|0;return(r==="x"?n:n&3|8).toString(16)}).toUpperCase()}return`${Ze()}-${e}`}function Ze(){let e=0,t=0;e=new Date().getTime()>>>0;try{t=performance.now()>>>0}catch{t=0}return Math.abs(e+t).toString(16).toLowerCase().padStart(8,"0")}function xe(e){const t=ze(e);return{[O]:t[O]||"",[x]:t[x]||""}}const P={PAGE_VIEW:"PAGE_VIEW",ADD_TO_CART:"ADD_TO_CART",PAGE_VIEW_2:"PAGE_VIEW_2",COLLECTION_VIEW:"COLLECTION_VIEW",PRODUCT_VIEW:"PRODUCT_VIEW",SEARCH_VIEW:"SEARCH_VIEW"},L={collection:"collection",product:"product",search:"search"},Xe={headless:"headless"},fe={hydrogen:"6167201",headless:"12875497473"};function I(e,t){return{schema_id:e,payload:t,metadata:{event_created_at_ms:Date.now()}}}function w(e){const t={id:"",resource:null,resourceId:null,search:"",searchParams:new URLSearchParams,hash:""};if(typeof e!="string")return t;try{const{search:r,searchParams:n,pathname:a,hash:i}=new URL(e),o=a.split("/"),s=o[o.length-1],c=o[o.length-2];return!s||!c?t:{id:`${s}${r}${i}`||"",resource:c??null,resourceId:s||null,search:r,searchParams:n,hash:i}}catch{return t}}function E(e,t){return typeof e!="object"?{}:(Object.entries(e).forEach(([r,n])=>{n&&(t[r]=n)}),t)}function et(e){return typeof document>"u"?(console.error(`${e} should only be used within the useEffect callback or event handlers`),!0):!1}const tt="trekkie_storefront_page_view/1.4",rt="myshopify.dev";function pe(e){const t=e,{id:r,resource:n}=w(t.resourceId),a=n?n.toLowerCase():void 0;return[I(tt,E({pageType:t.pageType,customerId:parseInt(w(t.customerId).id||"0"),resourceType:a,resourceId:parseInt(r)},nt(t)))]}function nt(e){return{appClientId:e.shopifySalesChannel?fe[e.shopifySalesChannel]:fe.headless,isMerchantRequest:ot(e.url),hydrogenSubchannelId:e.storefrontId||e.hydrogenSubchannelId||"0",isPersistentCookie:e.hasUserConsent,uniqToken:e.uniqueToken,visitToken:e.visitToken,microSessionId:H(),microSessionCount:1,url:e.url,path:e.path,search:e.search,referrer:e.referrer,title:e.title,shopId:parseInt(w(e.shopId).id),currency:e.currency,contentLanguage:e.acceptedLanguage||"en"}}function ot(e){if(typeof e!="string")return!1;const t=new URL(e).hostname;return t.indexOf(rt)!==-1||t==="localhost"}const at="2025.5.0",A="custom_storefront_customer_tracking/1.2",Le="page_rendered",Re="collection_page_rendered",Ne="product_page_rendered",it="product_added_to_cart",Ve="search_submitted";function U(e){return{canonical_url:e.canonicalUrl||e.url,customer_id:parseInt(w(e.customerId).id||"0")}}function st(e){const t=e,r=U(t),n=t.pageType,a=[];switch(a.push(I(A,E({event_name:Le,...r},S(t)))),n){case L.collection:a.push(I(A,E({event_name:Re,...r,collection_name:t.collectionHandle,collection_id:parseInt(w(t.collectionId).id)},S(t))));break;case L.product:a.push(I(A,E({event_name:Ne,...r,products:ee(t.products),total_value:t.totalValue},S(t))));break;case L.search:a.push(I(A,E({event_name:Ve,...r,search_string:t.searchString},S(t))));break}return a}function ct(e){const t=e,r=U(t);return[I(A,E({event_name:Le,...r},S(t)))]}function ut(e){const t=e,r=U(t);return[I(A,E({event_name:Re,...r,collection_name:t.collectionHandle,collection_id:parseInt(w(t.collectionId).id)},S(t)))]}function lt(e){const t=e,r=U(t);return[I(A,E({event_name:Ne,...r,products:ee(t.products),total_value:t.totalValue},S(t)))]}function dt(e){const t=e,r=U(t);return[I(A,E({event_name:Ve,...r,search_string:t.searchString},S(t)))]}function ft(e){const t=e,r=w(t.cartId);return[I(A,E({event_name:it,customerId:t.customerId,cart_token:r!=null&&r.id?`${r.id}`:null,total_value:t.totalValue,products:ee(t.products),customer_id:parseInt(w(t.customerId).id||"0")},S(t)))]}function S(e){return{source:e.shopifySalesChannel||Xe.headless,asset_version_id:e.assetVersionId||at,hydrogenSubchannelId:e.storefrontId||e.hydrogenSubchannelId||"0",is_persistent_cookie:e.hasUserConsent,deprecated_visit_token:e.visitToken,unique_token:e.uniqueToken,event_time:Date.now(),event_id:H(),event_source_url:e.url,referrer:e.referrer,user_agent:e.userAgent,navigation_type:e.navigationType,navigation_api:e.navigationApi,shop_id:parseInt(w(e.shopId).id),currency:e.currency,ccpa_enforced:e.ccpaEnforced||!1,gdpr_enforced:e.gdprEnforced||!1,gdpr_enforced_as_string:e.gdprEnforced?"true":"false",analytics_allowed:e.analyticsAllowed||!1,marketing_allowed:e.marketingAllowed||!1,sale_of_data_allowed:e.saleOfDataAllowed||!1}}function ee(e){return e?e.map(t=>{const r=E({variant_gid:t.variantGid,category:t.category,sku:t.sku,product_id:parseInt(w(t.productGid).id),variant_id:parseInt(w(t.variantGid).id)},{product_gid:t.productGid,name:t.name,variant:t.variantName||"",brand:t.brand,price:parseFloat(t.price),quantity:Number(t.quantity||0)});return JSON.stringify(r)}):[]}function q(e,t){const{eventName:r,payload:n}=e;if(!n.hasUserConsent)return Promise.resolve();let a=[];const i=n;return r===P.PAGE_VIEW?a=a.concat(pe(i),st(i)):r===P.ADD_TO_CART?a=a.concat(ft(n)):r===P.PAGE_VIEW_2?a=a.concat(pe(i),ct(i)):r===P.COLLECTION_VIEW?a=a.concat(ut(i)):r===P.PRODUCT_VIEW?a=a.concat(lt(i)):r===P.SEARCH_VIEW&&(a=a.concat(dt(i))),a.length?ht(a,t):Promise.resolve()}function pt(){return typeof window>"u"||!window.navigator?!1:/Chrome-Lighthouse/.test(window.navigator.userAgent)}const he="sendShopifyAnalytics request is unsuccessful";function ht(e,t){if(pt())return Promise.resolve();const r={events:e,metadata:{event_sent_at_ms:Date.now()}};try{return fetch(t?`https://${t}/.well-known/shopify/monorail/unstable/produce_batch`:"https://monorail-edge.shopifysvc.com/unstable/produce_batch",{method:"post",headers:{"content-type":"text/plain"},body:JSON.stringify(r)}).then(n=>{if(!n.ok)throw new Error("Response failed");return n.text()}).then(n=>{n&&JSON.parse(n).result.forEach(i=>{i.status!==200&&console.error(he,`

`,i.message)})}).catch(n=>{console.error(he,n)})}catch{return Promise.resolve()}}function mt(){if(et("getClientBrowserParameters"))return{uniqueToken:"",visitToken:"",url:"",path:"",search:"",referrer:"",title:"",userAgent:"",navigationType:"",navigationApi:""};const[e,t]=vt(),r=xe(document.cookie);return{uniqueToken:r[O],visitToken:r[x],url:location.href,path:location.pathname,search:location.search,referrer:document.referrer,title:document.title,userAgent:navigator.userAgent,navigationType:e,navigationApi:t}}function yt(){try{const e=(performance==null?void 0:performance.getEntriesByType)&&(performance==null?void 0:performance.getEntriesByType("navigation"));if(e&&e[0]){const t=window.performance.getEntriesByType("navigation")[0].type;return t&&t.toString()}}catch{}}function gt(){var e,t;try{if(PerformanceNavigation&&((e=performance==null?void 0:performance.navigation)==null?void 0:e.type)!==null&&((t=performance==null?void 0:performance.navigation)==null?void 0:t.type)!==void 0){const r=performance.navigation.type;switch(r){case PerformanceNavigation.TYPE_NAVIGATE:return"navigate";case PerformanceNavigation.TYPE_RELOAD:return"reload";case PerformanceNavigation.TYPE_BACK_FORWARD:return"back_forward";default:return`unknown: ${r}`}}}catch{}}function vt(){try{let e="PerformanceNavigationTiming",t=yt();return t||(t=gt(),e="performance.navigation"),t?[t,e]:["unknown","unknown"]}catch{}return["error","error"]}const me={};function _t(e,t){const r=me[e];if(r)return r;const n=new Promise((a,i)=>{const o=document.createElement("script");t!=null&&t.module?o.type="module":o.type="text/javascript",o.src=e,o.onload=()=>{a(!0)},o.onerror=()=>{i(!1)},(t==null?void 0:t.in)==="head"?document.head.appendChild(o):document.body.appendChild(o);const s=t==null?void 0:t.attributes;s&&Object.keys(s).forEach(c=>{o.setAttribute(c,s[c])})});return me[e]=n,n}function te(e,t){const[r,n]=d.useState("loading");return d.useEffect(()=>{_t(e,t).then(()=>n("done")).catch(()=>n("error"))},[e]),r}const wt=60*60*24*360*1,Pt=60*30;function Et(e){const{hasUserConsent:t=!1,domain:r="",checkoutDomain:n=""}=e||{};d.useEffect(()=>{const a=xe(document.cookie);let i=r||window.document.location.host;if(n){const s=n.split(".").reverse(),c=i.split(".").reverse(),u=[];s.forEach((l,f)=>{l===c[f]&&u.push(l)}),i=u.reverse().join(".")}/^localhost/.test(i)&&(i="");const o=i?/^\./.test(i)?i:`.${i}`:"";t?(j(O,a[O]||H(),wt,o),j(x,a[x]||H(),Pt,o)):(j(O,"",0,o),j(x,"",0,o))},[e,t,r,n])}function j(e,t,r,n){document.cookie=Qe(e,t,{maxage:r,domain:n,samesite:"Lax",path:"/"})}function J(e){let{type:t,data:r={},customData:n}=e,a=X(),{publish:i,cart:o,prevCart:s,shop:c,customData:u}=K(),l=a.pathname+a.search,f={...r,customData:{...u,...n},cart:o,prevCart:s,shop:c};return d.useEffect(()=>{c!=null&&c.shopId&&(f={...f,url:window.location.href},i(t,f))},[i,l,c==null?void 0:c.shopId]),null}function Ct(e){return _.jsx(J,{...e,type:"page_viewed"})}function It(e){return _.jsx(J,{...e,type:"product_viewed"})}function At(e){return _.jsx(J,{...e,type:"collection_viewed"})}function St(e){return _.jsx(J,{...e,type:"search_viewed"})}var C={PAGE_VIEWED:"page_viewed",PRODUCT_VIEWED:"product_viewed",COLLECTION_VIEWED:"collection_viewed",CART_VIEWED:"cart_viewed",SEARCH_VIEWED:"search_viewed",PRODUCT_ADD_TO_CART:"product_added_to_cart"},Tt="https://cdn.shopify.com/shopifycloud/consent-tracking-api/v0.1/consent-tracking-api.js",kt="https://cdn.shopify.com/shopifycloud/privacy-banner/storefront-banner.js";function Y(e){console.error(`[h2:error:useCustomerPrivacy] Unable to setup Customer Privacy API: Missing consent.${e} configuration.`)}function bt(e){let{withPrivacyBanner:t=!1,onVisitorConsentCollected:r,onReady:n,...a}=e;te(t?kt:Tt,{attributes:{id:"customer-privacy-api"}});let{observing:i,setLoaded:o}=Dt({withPrivacyBanner:t,onLoaded:n}),s=d.useMemo(()=>{let{checkoutDomain:u,storefrontAccessToken:l}=a;return u||Y("checkoutDomain"),l||Y("storefrontAccessToken"),(l.startsWith("shpat_")||l.length!==32)&&console.error("[h2:error:useCustomerPrivacy] It looks like you passed a private access token, make sure to use the public token"),{checkoutRootDomain:u,storefrontAccessToken:l,storefrontRootDomain:ve(u),country:a.country,locale:a.locale}},[a,ve,Y]);d.useEffect(()=>{let u=l=>{r&&r(l.detail)};return document.addEventListener("visitorConsentCollected",u),()=>{document.removeEventListener("visitorConsentCollected",u)}},[r]),d.useEffect(()=>{if(!t||i.current.privacyBanner)return;i.current.privacyBanner=!0;let u=window.privacyBanner||void 0;Object.defineProperty(window,"privacyBanner",{configurable:!0,get(){return u},set(l){if(typeof l=="object"&&l!==null&&"showPreferences"in l&&"loadBanner"in l){let f=l;f.loadBanner(s),u=we({privacyBanner:f,config:s}),o.privacyBanner(),ge()}}})},[t,s,we,o.privacyBanner]),d.useEffect(()=>{if(i.current.customerPrivacy)return;i.current.customerPrivacy=!0;let u=null,l=window.Shopify||void 0;Object.defineProperty(window,"Shopify",{configurable:!0,get(){return l},set(f){typeof f=="object"&&f!==null&&Object.keys(f).length===0&&(l=f,Object.defineProperty(window.Shopify,"customerPrivacy",{configurable:!0,get(){return u},set(p){if(typeof p=="object"&&p!==null&&"setTrackingConsent"in p){let y=p;u={...y,setTrackingConsent:_e({customerPrivacy:y,config:s})},l={...l,customerPrivacy:u},o.customerPrivacy(),ge()}}}))}})},[s,_e,o.customerPrivacy]);let c={customerPrivacy:F()};return t&&(c.privacyBanner=z()),c}var ye=!1;function ge(){if(ye)return;ye=!0;let e=new CustomEvent("shopifyCustomerPrivacyApiLoaded");document.dispatchEvent(e)}function Dt({withPrivacyBanner:e,onLoaded:t}){let r=d.useRef({customerPrivacy:!1,privacyBanner:!1}),[n,a]=d.useState(e?[!1,!1]:[!1]),i=n.every(Boolean),o={customerPrivacy:()=>{a(e?s=>[!0,s[1]]:()=>[!0])},privacyBanner:()=>{e&&a(s=>[s[0],!0])}};return d.useEffect(()=>{i&&t&&t()},[i,t]),{observing:r,setLoaded:o}}function ve(e){if(typeof window>"u")return;let t=window.document.location.host,r=e.split(".").reverse(),n=t.split(".").reverse(),a=[];return r.forEach((i,o)=>{i===n[o]&&a.push(i)}),a.reverse().join(".")}function _e({customerPrivacy:e,config:t}){let r=e.setTrackingConsent,{locale:n,country:a,...i}=t;function o(s,c){r({...i,headlessStorefront:!0,...s},c)}return o}function we({privacyBanner:e,config:t}){let r=e.loadBanner,n=e.showPreferences;function a(o){if(typeof o=="object"){r({...t,...o});return}r(t)}function i(o){if(typeof o=="object"){n({...t,...o});return}n(t)}return{loadBanner:a,showPreferences:i}}function F(){var e;try{return window.Shopify&&window.Shopify.customerPrivacy?(e=window.Shopify)==null?void 0:e.customerPrivacy:null}catch{return null}}function z(){try{return window&&(window!=null&&window.privacyBanner)?window.privacyBanner:null}catch{return null}}var Ot="2025.5.0";function xt(){let e=F();if(!e)throw new Error("Shopify Customer Privacy API not available. Must be used within a useEffect. Make sure to load the Shopify Customer Privacy API with useCustomerPrivacy() or <AnalyticsProvider>.");return e}function Lt({consent:e,onReady:t,domain:r}){let{subscribe:n,register:a,canTrack:i}=K(),[o,s]=d.useState(!1),[c,u]=d.useState(!1),l=d.useRef(!1),{checkoutDomain:f,storefrontAccessToken:p,language:y}=e,{ready:g}=a("Internal_Shopify_Analytics");return bt({...e,locale:y,checkoutDomain:f||"mock.shop",storefrontAccessToken:p||"abcdefghijklmnopqrstuvwxyz123456",onVisitorConsentCollected:()=>u(!0),onReady:()=>u(!0)}),Et({hasUserConsent:c?i():!0,domain:r,checkoutDomain:f}),d.useEffect(()=>{l.current||(l.current=!0,n(C.PAGE_VIEWED,Nt),n(C.PRODUCT_VIEWED,Vt),n(C.COLLECTION_VIEWED,Ut),n(C.SEARCH_VIEWED,qt),n(C.PRODUCT_ADD_TO_CART,$t),s(!0))},[n]),d.useEffect(()=>{o&&c&&(g(),t())},[o,c,t]),null}function B(e){console.error(`[h2:error:ShopifyAnalytics] Unable to send Shopify analytics: Missing shop.${e} configuration.`)}function $(e){var n,a,i,o;let t=xt(),r=t.analyticsProcessingAllowed();if(!((n=e==null?void 0:e.shop)!=null&&n.shopId)){B("shopId");return}if(!((a=e==null?void 0:e.shop)!=null&&a.acceptedLanguage)){B("acceptedLanguage");return}if(!((i=e==null?void 0:e.shop)!=null&&i.currency)){B("currency");return}if(!((o=e==null?void 0:e.shop)!=null&&o.hydrogenSubchannelId)){B("hydrogenSubchannelId");return}return{shopifySalesChannel:"hydrogen",assetVersionId:Ot,...e.shop,hasUserConsent:r,...mt(),ccpaEnforced:!t.saleOfDataAllowed(),gdprEnforced:!(t.marketingAllowed()&&t.analyticsProcessingAllowed()),analyticsAllowed:t.analyticsProcessingAllowed(),marketingAllowed:t.marketingAllowed(),saleOfDataAllowed:t.saleOfDataAllowed()}}function Rt(e,t){if(t===null)return;let r=$(e);return r?{...r,cartId:t.id}:void 0}var T={};function Nt(e){let t=$(e);t&&(q({eventName:P.PAGE_VIEW_2,payload:{...t,...T}}),T={})}function Vt(e){let t=$(e);if(t&&Ue({type:"product",products:e.products})){let r=Q(e.products);T={pageType:L.product,resourceId:r[0].productGid},t={...t,...T,products:Q(e.products)},q({eventName:P.PRODUCT_VIEW,payload:t})}}function Ut(e){let t=$(e);t&&(T={pageType:L.collection,resourceId:e.collection.id},t={...t,...T,collectionHandle:e.collection.handle,collectionId:e.collection.id},q({eventName:P.COLLECTION_VIEW,payload:t}))}function qt(e){let t=$(e);t&&(T={pageType:L.search},t={...t,...T,searchString:e.searchTerm},q({eventName:P.SEARCH_VIEW,payload:t}))}function $t(e){let{cart:t,currentLine:r}=e,n=Rt(e,t);!n||!(r!=null&&r.id)||jt({matchedLine:r,eventPayload:n})}function jt({matchedLine:e,eventPayload:t}){let r={id:e.merchandise.product.id,variantId:e.merchandise.id,title:e.merchandise.product.title,variantTitle:e.merchandise.title,vendor:e.merchandise.product.vendor,price:e.merchandise.price.amount,quantity:e.quantity,productType:e.merchandise.product.productType,sku:e.merchandise.sku};Ue({type:"cart",products:[r]})&&q({eventName:P.ADD_TO_CART,payload:{...t,products:Q([r])}})}function k(e,t,r,n){if(e==="cart"){let a=`${r?"merchandise":"merchandise.product"}.${t}`;console.error(`[h2:error:ShopifyAnalytics] Can't set up cart analytics events because the \`cart.lines[].${a}\` value is missing from your GraphQL cart query. In your project, search for where \`fragment CartLine on CartLine\` is defined and make sure \`${a}\` is part of your cart query. Check the Hydrogen Skeleton template for reference: https://github.com/Shopify/hydrogen/blob/main/templates/skeleton/app/lib/fragments.ts#L25-L56.`)}else{let a=`${n||t}`;console.error(`[h2:error:ShopifyAnalytics] Can't set up product view analytics events because the \`${a}\` is missing from your \`<Analytics.ProductView>\`. Make sure \`${a}\` is part of your products data prop. Check the Hydrogen Skeleton template for reference: https://github.com/Shopify/hydrogen/blob/main/templates/skeleton/app/routes/products.%24handle.tsx#L159-L165.`)}}function Ue({type:e,products:t}){return!t||t.length===0?(k(e,"",!1,"data.products"),!1):(t.forEach(r=>{if(!r.id)return k(e,"id",!1),!1;if(!r.title)return k(e,"title",!1),!1;if(!r.price)return k(e,"price.amount",!0,"price"),!1;if(!r.vendor)return k(e,"vendor",!1),!1;if(!r.variantId)return k(e,"id",!0,"variantId"),!1;if(!r.variantTitle)return k(e,"title",!0,"variantTitle"),!1}),!0)}function Q(e){return e.map(t=>{let r={productGid:t.id,variantGid:t.variantId,name:t.title,variantName:t.variantTitle,brand:t.vendor,price:t.price,quantity:t.quantity||1,category:t.productType};return t.sku&&(r.sku=t.sku),t.productType&&(r.category=t.productType),r})}function Pe(e){console.error(`[h2:error:CartAnalytics] Can't set up cart analytics events because the \`cart.${e}\` value is missing from your GraphQL cart query. In your project, search for where \`fragment CartApiQuery on Cart\` is defined and make sure \`${e}\` is part of your cart query. Check the Hydrogen Skeleton template for reference: https://github.com/Shopify/hydrogen/blob/main/templates/skeleton/app/lib/fragments.ts#L59.`)}function Bt({cart:e,setCarts:t}){let{publish:r,shop:n,customData:a,canTrack:i,cart:o,prevCart:s}=K(),c=d.useRef(null);return d.useEffect(()=>{if(e)return Promise.resolve(e).then(u=>{if(u&&u.lines){if(!u.id){Pe("id");return}if(!u.updatedAt){Pe("updatedAt");return}}t(({cart:l,prevCart:f})=>(u==null?void 0:u.updatedAt)!==(l==null?void 0:l.updatedAt)?{cart:u,prevCart:l}:{cart:l,prevCart:f})}),()=>{}},[t,e]),d.useEffect(()=>{if(!o||!(o!=null&&o.updatedAt)||(o==null?void 0:o.updatedAt)===(s==null?void 0:s.updatedAt))return;let u;try{u=JSON.parse(localStorage.getItem("cartLastUpdatedAt")||"")}catch{u=null}if(o.id===(u==null?void 0:u.id)&&o.updatedAt===(u==null?void 0:u.updatedAt))return;let l={eventTimestamp:Date.now(),cart:o,prevCart:s,shop:n,customData:a};if(o.updatedAt===c.current)return;c.current=o.updatedAt,r("cart_updated",l),localStorage.setItem("cartLastUpdatedAt",JSON.stringify({id:o.id,updatedAt:o.updatedAt}));let f=s!=null&&s.lines?N(s==null?void 0:s.lines):[],p=o.lines?N(o.lines):[];f==null||f.forEach(y=>{let g=p.filter(h=>y.id===h.id);if((g==null?void 0:g.length)===1){let h=g[0];y.quantity<h.quantity?r("product_added_to_cart",{...l,prevLine:y,currentLine:h}):y.quantity>h.quantity&&r("product_removed_from_cart",{...l,prevLine:y,currentLine:h})}else r("product_removed_from_cart",{...l,prevLine:y})}),p==null||p.forEach(y=>{let g=f.filter(h=>y.id===h.id);(!g||g.length===0)&&r("product_added_to_cart",{...l,currentLine:y})})},[o,s,r,n,a,i]),null}var Mt="https://cdn.shopify.com/shopifycloud/perf-kit/shopify-perf-kit-spa.min.js";function Wt({shop:e}){let t=d.useRef(!1),{subscribe:r,register:n}=K(),{ready:a}=n("Internal_Shopify_Perf_Kit"),i=te(Mt,{attributes:{id:"perfkit","data-application":"hydrogen","data-shop-id":w(e.shopId).id.toString(),"data-storefront-id":e.hydrogenSubchannelId,"data-monorail-region":"global","data-spa-mode":"true","data-resource-timing-sampling-rate":"100"}});return d.useEffect(()=>{i!=="done"||t.current||(t.current=!0,r(C.PAGE_VIEWED,()=>{var o;(o=window.PerfKit)==null||o.navigate()}),r(C.PRODUCT_VIEWED,()=>{var o;(o=window.PerfKit)==null||o.setPageType("product")}),r(C.COLLECTION_VIEWED,()=>{var o;(o=window.PerfKit)==null||o.setPageType("collection")}),r(C.SEARCH_VIEWED,()=>{var o;(o=window.PerfKit)==null||o.setPageType("search")}),r(C.CART_VIEWED,()=>{var o;(o=window.PerfKit)==null||o.setPageType("cart")}),a())},[r,a,i]),null}var Ee=new Set,Gt=e=>{Ee.has(e)||(console.warn(e),Ee.add(e))},Ce=new Set,Ie=e=>{Ce.has(e)||(console.error(new Error(e)),Ce.add(e))},Ht={canTrack:()=>!1,cart:null,customData:{},prevCart:null,publish:()=>{},shop:null,subscribe:()=>{},register:()=>({ready:()=>{}}),customerPrivacy:null,privacyBanner:null},qe=d.createContext(Ht),W=new Map,V={};function $e(){return Object.values(V).every(Boolean)}function Ae(e,t){var r;W.has(e)||W.set(e,new Map),(r=W.get(e))==null||r.set(t.toString(),t)}var G=new Map;function Se(e,t){if(!$e()){G.set(e,t);return}je(e,t)}function je(e,t){(W.get(e)??new Map).forEach((r,n)=>{try{r(t)}catch(a){typeof a=="object"&&a instanceof Error?console.error("Analytics publish error",a.message,n,a.stack):console.error("Analytics publish error",a,n)}})}function Te(e){return V.hasOwnProperty(e)||(V[e]=!1),{ready:()=>{V[e]=!0,$e()&&G.size>0&&(G.forEach((t,r)=>{je(r,t)}),G.clear())}}}function ke(){try{return window.Shopify.customerPrivacy.analyticsProcessingAllowed()}catch{}return!1}function be(e,t){return`[h2:error:Analytics.Provider] - ${e} is required. Make sure ${t} is defined in your environment variables. See https://h2o.fyi/analytics/consent to learn how to setup environment variables in the Shopify admin.`}function Ft({canTrack:e,cart:t,children:r,consent:n,customData:a={},shop:i=null,cookieDomain:o}){var m;let s=d.useRef(!1),{shop:c}=Jt(i),[u,l]=d.useState(!!e),[f,p]=d.useState({cart:null,prevCart:null}),[y,g]=d.useState(e?()=>e:()=>ke);if(c)if(/\/68817551382$/.test(c.shopId))Gt("[h2:error:Analytics.Provider] - Mock shop is used. Analytics will not work properly.");else{if(!n.checkoutDomain){let v=be("consent.checkoutDomain","PUBLIC_CHECKOUT_DOMAIN");Ie(v)}if(!n.storefrontAccessToken){let v=be("consent.storefrontAccessToken","PUBLIC_STOREFRONT_API_TOKEN");Ie(v)}n!=null&&n.country||(n.country="US"),n!=null&&n.language||(n.language="EN"),n.withPrivacyBanner===void 0&&(n.withPrivacyBanner=!1)}let h=d.useMemo(()=>({canTrack:y,...f,customData:a,publish:y()?Se:()=>{},shop:c,subscribe:Ae,register:Te,customerPrivacy:F(),privacyBanner:z()}),[u,y,f,(m=f.cart)==null?void 0:m.updatedAt,f.prevCart,Se,Ae,a,c,Te,JSON.stringify(V),F,z]);return _.jsxs(qe.Provider,{value:h,children:[r,!!c&&_.jsx(Ct,{}),!!c&&!!t&&_.jsx(Bt,{cart:t,setCarts:p}),!!c&&n.checkoutDomain&&_.jsx(Lt,{consent:n,onReady:()=>{s.current=!0,l(!0),g(e?()=>e:()=>ke)},domain:o}),!!c&&_.jsx(Wt,{shop:c})]})}function K(){let e=d.useContext(qe);if(!e)throw new Error("[h2:error:useAnalytics] 'useAnalytics()' must be a descendent of <AnalyticsProvider/>");return e}function Jt(e){let[t,r]=d.useState(null);return d.useEffect(()=>(Promise.resolve(e).then(r),()=>{}),[r,e]),{shop:t}}var tr={CollectionView:At,ProductView:It,Provider:Ft,SearchView:St},Be="cartFormInput";function b({children:e,action:t,inputs:r,route:n,fetcherKey:a}){let i=Ge({key:a});return _.jsxs(i.Form,{action:n||"",method:"post",children:[(t||r)&&_.jsx("input",{type:"hidden",name:Be,value:JSON.stringify({action:t,inputs:r})}),typeof e=="function"?e(i):e]})}b.INPUT_NAME=Be;b.ACTIONS={AttributesUpdateInput:"AttributesUpdateInput",BuyerIdentityUpdate:"BuyerIdentityUpdate",Create:"Create",DiscountCodesUpdate:"DiscountCodesUpdate",GiftCardCodesUpdate:"GiftCardCodesUpdate",LinesAdd:"LinesAdd",LinesRemove:"LinesRemove",LinesUpdate:"LinesUpdate",NoteUpdate:"NoteUpdate",SelectedDeliveryOptionsUpdate:"SelectedDeliveryOptionsUpdate",MetafieldsSet:"MetafieldsSet",MetafieldDelete:"MetafieldDelete",DeliveryAddressesAdd:"DeliveryAddressesAdd",DeliveryAddressesUpdate:"DeliveryAddressesUpdate",DeliveryAddressesRemove:"DeliveryAddressesRemove"};function Kt(e){let t={};for(let o of e.entries()){let s=o[0],c=e.getAll(s);t[s]=c.length>1?c:o[1],t[s]==="on"?t[s]=!0:t[s]==="off"&&(t[s]=!1)}let{cartFormInput:r,...n}=t,{action:a,inputs:i}=r?JSON.parse(String(r)):{};return{action:a,inputs:{...i,...n}}}b.getFormInput=Kt;var Me="__h_pending_";function Yt(e){return Me+e}function De(e){return e.startsWith(Me)}function rr(e){let t=He();if(!t||!t.length)return e;let r=e!=null&&e.lines?structuredClone(e):{lines:{nodes:[]}},n=r.lines.nodes,a=!1;for(let{formData:i}of t){if(!i)continue;let o=b.getFormInput(i);if(o.action===b.ACTIONS.LinesAdd)for(let s of o.inputs.lines){if(!s.selectedVariant){console.error("[h2:error:useOptimisticCart] No selected variant was passed in the cart action. Make sure to pass the selected variant if you want to use an optimistic cart");continue}let c=n.find(u=>{var l;return u.merchandise.id===((l=s.selectedVariant)==null?void 0:l.id)});a=!0,c?(c.quantity=(c.quantity||1)+(s.quantity||1),c.isOptimistic=!0):n.unshift({id:Yt(s.selectedVariant.id),merchandise:s.selectedVariant,isOptimistic:!0,quantity:s.quantity||1})}else if(o.action===b.ACTIONS.LinesRemove)for(let s of o.inputs.lineIds){let c=n.findIndex(u=>u.id===s);if(c!==-1){if(De(n[c].id)){console.error("[h2:error:useOptimisticCart] Tried to remove an optimistic line that has not been added to the cart yet");continue}n.splice(c,1),a=!0}else console.warn(`[h2:warn:useOptimisticCart] Tried to remove line '${s}' but it doesn't exist in the cart`)}else if(o.action===b.ACTIONS.LinesUpdate)for(let s of o.inputs.lines){let c=n.findIndex(u=>s.id===u.id);if(c>-1){if(De(n[c].id)){console.error("[h2:error:useOptimisticCart] Tried to update an optimistic line that has not been added to the cart yet");continue}n[c].quantity=s.quantity,n[c].quantity===0&&n.splice(c,1),a=!0}else console.warn(`[h2:warn:useOptimisticCart] Tried to update line '${s.id}' but it doesn't exist in the cart`)}}return a&&(r.isOptimistic=a),r.totalQuantity=n.reduce((i,o)=>i+o.quantity,0),r}var We=d.createContext(void 0);We.Provider;var zt=()=>d.useContext(We);d.forwardRef((e,t)=>{let{waitForHydration:r,src:n,...a}=e,i=zt();return r?_.jsx(Qt,{src:n,options:a}):_.jsx("script",{suppressHydrationWarning:!0,...a,src:n,nonce:i,ref:t})});function Qt({src:e,options:t}){if(!e)throw new Error("`waitForHydration` with the Script component requires a `src` prop");return te(e,{attributes:t}),null}function nr({connection:e,children:t=()=>(console.warn("<Pagination> requires children to work properly"),null),namespace:r=""}){let[n,a]=d.useState(!1),i=Z(),o=X();Oe(),d.useEffect(()=>{i.state==="idle"&&a(!1)},[i.state]);let{endCursor:s,hasNextPage:c,hasPreviousPage:u,nextPageUrl:l,nodes:f,previousPageUrl:p,startCursor:y}=Zt(e,r),g=d.useMemo(()=>{var v;return{...o.state,pagination:{...((v=o.state)==null?void 0:v.pagination)||{},[r]:{pageInfo:{endCursor:s,hasPreviousPage:u,hasNextPage:c,startCursor:y},nodes:f}}}},[s,c,u,y,f,r,o.state]),h=d.useMemo(()=>d.forwardRef(function(v,D){return c?d.createElement(ue,{preventScrollReset:!0,...v,to:l,state:g,replace:!0,ref:D,onClick:()=>a(!0)}):null}),[c,l,g]),m=d.useMemo(()=>d.forwardRef(function(v,D){return u?d.createElement(ue,{preventScrollReset:!0,...v,to:p,state:g,replace:!0,ref:D,onClick:()=>a(!0)}):null}),[u,p,g]);return t({state:g,hasNextPage:c,hasPreviousPage:u,isLoading:n,nextPageUrl:l,nodes:f,previousPageUrl:p,NextLink:h,PreviousLink:m})}function M(e,t){let r=new URLSearchParams(e);return Object.keys((t==null?void 0:t.pagination)||{}).forEach(n=>{let a=n===""?"":`${n}_`,i=`${a}cursor`,o=`${a}direction`;r.delete(i),r.delete(o)}),r.toString()}function R(e){throw new Error(`The Pagination component requires ${"`"+e+"`"} to be a part of your query. See the guide on how to setup your query to include ${"`"+e+"`"}: https://shopify.dev/docs/custom-storefronts/hydrogen/data-fetching/pagination#setup-the-paginated-query`)}function Zt(e,t=""){e.pageInfo||R("pageInfo"),typeof e.pageInfo.startCursor>"u"&&R("pageInfo.startCursor"),typeof e.pageInfo.endCursor>"u"&&R("pageInfo.endCursor"),typeof e.pageInfo.hasNextPage>"u"&&R("pageInfo.hasNextPage"),typeof e.pageInfo.hasPreviousPage>"u"&&R("pageInfo.hasPreviousPage");let r=Z(),n=Oe(),{state:a,search:i,pathname:o}=X(),s=t?`${t}_cursor`:"cursor",c=t?`${t}_direction`:"direction",u=new URLSearchParams(i).get(c)==="previous",l=d.useMemo(()=>{var h,m,v;return!((h=globalThis==null?void 0:globalThis.window)!=null&&h.__hydrogenHydrated)||!((v=(m=a==null?void 0:a.pagination)==null?void 0:m[t])!=null&&v.nodes)?N(e):u?[...N(e),...a.pagination[t].nodes||[]]:[...a.pagination[t].nodes||[],...N(e)]},[a,e,t]),f=d.useMemo(()=>{var oe,ae,ie,se,ce;let h=(oe=globalThis==null?void 0:globalThis.window)==null?void 0:oe.__hydrogenHydrated,m=(ie=(ae=a==null?void 0:a.pagination)==null?void 0:ae[t])==null?void 0:ie.pageInfo,v=!h||(m==null?void 0:m.startCursor)===void 0?e.pageInfo.startCursor:m.startCursor,D=!h||(m==null?void 0:m.endCursor)===void 0?e.pageInfo.endCursor:m.endCursor,re=!h||(m==null?void 0:m.hasPreviousPage)===void 0?e.pageInfo.hasPreviousPage:m.hasPreviousPage,ne=!h||(m==null?void 0:m.hasNextPage)===void 0?e.pageInfo.hasNextPage:m.hasNextPage;return(ce=(se=a==null?void 0:a.pagination)==null?void 0:se[t])!=null&&ce.nodes&&(u?(v=e.pageInfo.startCursor,re=e.pageInfo.hasPreviousPage):(D=e.pageInfo.endCursor,ne=e.pageInfo.hasNextPage)),{startCursor:v,endCursor:D,hasPreviousPage:re,hasNextPage:ne}},[u,a,t,e.pageInfo.hasNextPage,e.pageInfo.hasPreviousPage,e.pageInfo.startCursor,e.pageInfo.endCursor]),p=d.useRef({params:M(i,a),pathname:o});d.useEffect(()=>{window.__hydrogenHydrated=!0},[]),d.useEffect(()=>{let h=M(i,a),m=p.current.params;(o!==p.current.pathname||h!==m)&&!(r.state==="idle"&&!r.location)&&(p.current={pathname:o,params:M(i,a)},n(`${o}?${M(i,a)}`,{replace:!0,preventScrollReset:!0,state:{nodes:void 0,pageInfo:void 0}}))},[o,i,a]);let y=d.useMemo(()=>{let h=new URLSearchParams(i);return h.set(c,"previous"),f.startCursor&&h.set(s,f.startCursor),`?${h.toString()}`},[i,f.startCursor]),g=d.useMemo(()=>{let h=new URLSearchParams(i);return h.set(c,"next"),f.endCursor&&h.set(s,f.endCursor),`?${h.toString()}`},[i,f.endCursor]);return{...f,previousPageUrl:y,nextPageUrl:g,nodes:l}}function or(e,t){let r=Z(),[n,a]=d.useState([]);if(d.useEffect(()=>{Promise.resolve(t).then(i=>{var o,s;i&&a(i instanceof Array?i:((s=(o=i.product)==null?void 0:o.variants)==null?void 0:s.nodes)||[])}).catch(i=>{reportError(new Error("[h2:error:useOptimisticVariant] An error occurred while resolving the variants for the optimistic product hook.",{cause:i}))})},[JSON.stringify(t)]),r.state==="loading"){let i=new URLSearchParams(r.location.search),o=!1,s=n.find(c=>c.selectedOptions?c.selectedOptions.every(u=>i.get(u.name)===u.value):(o||(o=!0,reportError(new Error("[h2:error:useOptimisticVariant] The optimistic product hook requires your product query to include variants with the selectedOptions field."))),!1));if(s)return{...s,isOptimistic:!0}}return e}d.lazy(()=>Ke(()=>import("./log-seo-tags-TY72EQWZ-DCHH-_Gl.js"),[]));export{zt as $,K as G,rr as J,Ke as _,N as f,b as j,nr as k,or as q,tr as s};
