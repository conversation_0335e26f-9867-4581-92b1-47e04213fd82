import {Link, useNavigate} from 'react-router';
import {type MappedProductOptions} from '@shopify/hydrogen';
import type {
  Maybe,
  ProductOptionValueSwatch,
} from '@shopify/hydrogen/storefront-api-types';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductItemFragment, IndividualProductFragment} from 'storefrontapi.generated';
import {useState} from 'react';

export function ProductForm({
  productOptions,
  selectedVariant,
  product,
  hideProductOptions = false,
  hideQuantitySelector = false,
  showOnlyOptions = false,
}: {
  productOptions: MappedProductOptions[];
  selectedVariant: IndividualProductFragment['selectedOrFirstAvailableVariant'];
  product?: IndividualProductFragment;
  hideProductOptions?: boolean;
  hideQuantitySelector?: boolean;
  showOnlyOptions?: boolean;
}) {
  const navigate = useNavigate();
  const {open} = useAside();
  const [quantity, setQuantity] = useState(1);
  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>(null);
  const [selectedPurchaseType, setSelectedPurchaseType] = useState<'onetime' | 'subscription'>('onetime');



  // If showOnlyOptions is true, only show product options and quantity selector
  if (showOnlyOptions) {
    return (
      <div className="product-form space-y-3">
        {/* Product Options */}
        {productOptions.map((option) => {
          // If there is only a single value in the option values, don't display the option
          if (option.optionValues.length === 1) return null;

          return (
            <div className="space-y-1" key={option.name}>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-[#10b981] to-[#059669] rounded-full shadow-sm"></div>
                <h3 className="text-xs font-semibold !text-[#047857] uppercase tracking-wide">{option.name}</h3>
              </div>
              <div className="flex flex-wrap gap-1">
                {option.optionValues.map((value) => {
                  const {
                    name,
                    handle,
                    variantUriQuery,
                    selected,
                    available,
                    exists,
                    isDifferentProduct,
                    swatch,
                  } = value;

                  // Enhanced styling for variant buttons with bright emerald color accents
                  const baseClasses = `px-3 py-1.5 rounded-lg text-xs font-semibold transition-all duration-200 border-2 relative overflow-hidden ${
                    selected
                      ? 'bg-gradient-to-r from-[#10b981] to-[#059669] text-white border-[#10b981] shadow-lg transform scale-105 ring-2 ring-[#10b981]/40'
                      : 'bg-gradient-to-r from-white to-emerald-50 !text-gray-700 hover:from-emerald-100 hover:to-emerald-200 border-gray-300 hover:border-[#10b981] hover:shadow-md hover:scale-[1.02] active:scale-[0.98] hover:text-[#047857]'
                  } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}`;

                  if (isDifferentProduct) {
                    return (
                      <Link
                        className={baseClasses}
                        key={option.name + name}
                        prefetch="intent"
                        preventScrollReset
                        replace
                        to={`/products/${handle}?${variantUriQuery}`}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
                        <span className="relative">
                          {swatch?.color || swatch?.image ? (
                            <ProductOptionSwatch swatch={swatch} name={name} />
                          ) : (
                            name
                          )}
                        </span>
                      </Link>
                    );
                  } else {
                    return (
                      <button
                        type="button"
                        className={baseClasses}
                        key={option.name + name}
                        disabled={!exists}
                        onClick={() => {
                          if (!selected) {
                            navigate(`?${variantUriQuery}`, {
                              replace: true,
                              preventScrollReset: true,
                            });
                          }
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
                        <span className="relative">
                          {swatch?.color || swatch?.image ? (
                            <ProductOptionSwatch swatch={swatch} name={name} />
                          ) : (
                            name
                          )}
                        </span>
                      </button>
                    );
                  }
                })}
              </div>
            </div>
          );
        })}

        {/* Quantity Selector */}
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gradient-to-r from-[#10b981] to-[#059669] rounded-full shadow-sm"></div>
            <h3 className="text-xs font-semibold !text-[#047857] uppercase tracking-wide">Quantity</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="w-8 h-8 rounded-lg border-2 border-gray-300 bg-gradient-to-r from-white to-emerald-50 flex items-center justify-center hover:from-emerald-100 hover:to-emerald-200 hover:border-[#10b981] transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed shadow-sm hover:shadow-md transform hover:scale-[1.05] active:scale-[0.95]"
              disabled={quantity <= 1}
            >
              <svg className="w-3 h-3 !text-emerald-600 hover:!text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M20 12H4" />
              </svg>
            </button>
            <div className="w-12 text-center font-bold !text-emerald-800 bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-1.5 rounded-lg text-sm border-2 border-emerald-200 shadow-sm">
              {quantity}
            </div>
            <button
              type="button"
              onClick={() => setQuantity(quantity + 1)}
              className="w-8 h-8 rounded-lg border-2 border-gray-300 bg-gradient-to-r from-white to-emerald-50 flex items-center justify-center hover:from-emerald-100 hover:to-emerald-200 hover:border-[#10b981] transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.05] active:scale-[0.95]"
            >
              <svg className="w-3 h-3 !text-emerald-600 hover:!text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="product-form space-y-3">
      {/* Product Options */}
      {!hideProductOptions && productOptions.map((option) => {
        // If there is only a single value in the option values, don't display the option
        if (option.optionValues.length === 1) return null;

        return (
          <div className="space-y-1" key={option.name}>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gradient-to-r from-[#10b981] to-[#059669] rounded-full shadow-sm"></div>
              <h3 className="text-xs font-semibold !text-[#047857] uppercase tracking-wide">{option.name}</h3>
            </div>
            <div className="flex flex-wrap gap-1">
              {option.optionValues.map((value) => {
                const {
                  name,
                  handle,
                  variantUriQuery,
                  selected,
                  available,
                  exists,
                  isDifferentProduct,
                  swatch,
                } = value;

                // Enhanced styling for variant buttons with bright emerald color accents
                const baseClasses = `px-3 py-1.5 rounded-lg text-xs font-semibold transition-all duration-200 border-2 relative overflow-hidden ${
                  selected
                    ? 'bg-gradient-to-r from-[#10b981] to-[#059669] text-white border-[#10b981] shadow-lg transform scale-105 ring-2 ring-[#10b981]/40'
                    : 'bg-gradient-to-r from-white to-emerald-50 !text-gray-700 hover:from-emerald-100 hover:to-emerald-200 border-gray-300 hover:border-[#10b981] hover:shadow-md hover:scale-[1.02] active:scale-[0.98] hover:text-[#047857]'
                } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}`;

                if (isDifferentProduct) {
                  // SEO
                  // When the variant is a combined listing child product
                  // that leads to a different url, we need to render it
                  // as an anchor tag
                  return (
                    <Link
                      className={baseClasses}
                      key={option.name + name}
                      prefetch="intent"
                      preventScrollReset
                      replace
                      to={`/products/${handle}?${variantUriQuery}`}
                    >
                      {/* Subtle shimmer effect on hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
                      <span className="relative">
                        {swatch?.color || swatch?.image ? (
                          <ProductOptionSwatch swatch={swatch} name={name} />
                        ) : (
                          name
                        )}
                      </span>
                    </Link>
                  );
                } else {
                  // SEO
                  // When the variant is an update to the search param,
                  // render it as a button with javascript navigating to
                  // the variant so that SEO bots do not index these as
                  // duplicated links
                  return (
                    <button
                      type="button"
                      className={baseClasses}
                      key={option.name + name}
                      disabled={!exists}
                      onClick={() => {
                        if (!selected) {
                          navigate(`?${variantUriQuery}`, {
                            replace: true,
                            preventScrollReset: true,
                          });
                        }
                      }}
                    >
                      {/* Subtle shimmer effect on hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
                      <span className="relative">
                        {swatch?.color || swatch?.image ? (
                          <ProductOptionSwatch swatch={swatch} name={name} />
                        ) : (
                          name
                        )}
                      </span>
                    </button>
                  );
                }
              })}
            </div>
          </div>
        );
      })}

      {/* Quantity Selector */}
      {!hideQuantitySelector && (
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gradient-to-r from-[#10b981] to-[#059669] rounded-full shadow-sm"></div>
            <h3 className="text-xs font-semibold !text-[#047857] uppercase tracking-wide">Quantity</h3>
          </div>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-8 h-8 rounded-lg border-2 border-gray-300 bg-gradient-to-r from-white to-emerald-50 flex items-center justify-center hover:from-emerald-100 hover:to-emerald-200 hover:border-[#10b981] transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed shadow-sm hover:shadow-md transform hover:scale-[1.05] active:scale-[0.95]"
            disabled={quantity <= 1}
          >
            <svg className="w-3 h-3 !text-emerald-600 hover:!text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M20 12H4" />
            </svg>
          </button>
          <div className="w-12 text-center font-bold !text-emerald-800 bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-1.5 rounded-lg text-sm border-2 border-emerald-200 shadow-sm">
            {quantity}
          </div>
          <button
            type="button"
            onClick={() => setQuantity(quantity + 1)}
            className="w-8 h-8 rounded-lg border-2 border-gray-300 bg-gradient-to-r from-white to-emerald-50 flex items-center justify-center hover:from-emerald-100 hover:to-emerald-200 hover:border-[#10b981] transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.05] active:scale-[0.95]"
          >
            <svg className="w-3 h-3 !text-emerald-600 hover:!text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
        </div>
      )}

      {/* Sales-Optimized Purchase Options */}
      {product && selectedVariant && (
        <SalesOptimizedPurchaseOptions
          product={product}
          selectedVariant={selectedVariant}
          quantity={quantity}
          selectedPurchaseType={selectedPurchaseType}
          setSelectedPurchaseType={setSelectedPurchaseType}
          selectedSellingPlanId={selectedSellingPlanId}
          setSelectedSellingPlanId={setSelectedSellingPlanId}
        />
      )}
    </div>
  );
}

function SalesOptimizedPurchaseOptions({
  product,
  selectedVariant,
  quantity,
  selectedPurchaseType,
  setSelectedPurchaseType,
  selectedSellingPlanId,
  setSelectedSellingPlanId,
}: {
  product: IndividualProductFragment;
  selectedVariant: IndividualProductFragment['selectedOrFirstAvailableVariant'];
  quantity: number;
  selectedPurchaseType: 'onetime' | 'subscription';
  setSelectedPurchaseType: (type: 'onetime' | 'subscription') => void;
  selectedSellingPlanId: string | null;
  setSelectedSellingPlanId: (id: string | null) => void;
}) {
  const {open} = useAside();

  // Subscription options
  const subscriptionOptions = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
      discount: '15%'
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
      discount: '15%'
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: 'Delivered every 3 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
      discount: '15%'
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: 'Delivered every 6 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
      discount: '15%'
    }
  ];

  // Check if product has selling plans
  const hasSellingPlans = product?.sellingPlanGroups?.nodes && product.sellingPlanGroups.nodes.length > 0;

  // Calculate savings for quantity purchases
  const calculateSavings = (qty: number) => {
    if (qty >= 3) return 10;
    if (qty >= 2) return 5;
    return 0;
  };

  const basePrice = selectedVariant?.price ? parseFloat(selectedVariant.price.amount) : 0;

  return (
    <div className="space-y-3">
      {/* Enhanced Purchase Type Selection */}
      <div className="space-y-2">
        <div className="text-center">
          <h3 className="text-xs font-semibold !text-gray-700 uppercase tracking-wide mb-1">How would you like to purchase?</h3>
        </div>

        {/* One-time vs Subscription Toggle */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <button
            type="button"
            onClick={() => setSelectedPurchaseType('onetime')}
            className={`p-3 rounded-xl border-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] ${
              selectedPurchaseType === 'onetime'
                ? 'border-[#10b981] bg-gradient-to-br from-emerald-100 to-emerald-50 text-[#047857] shadow-lg ring-2 ring-[#10b981]/20'
                : 'border-gray-200 bg-gradient-to-br from-white to-gray-50 text-gray-700 hover:border-[#10b981]/50 hover:shadow-md'
            }`}
          >
            <div className="text-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 transition-all duration-300 ${
                selectedPurchaseType === 'onetime'
                  ? 'bg-gradient-to-r from-[#10b981] to-[#059669] shadow-md'
                  : 'bg-gradient-to-r from-gray-400 to-gray-500'
              }`}>
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="font-bold text-sm">One-time Purchase</div>
              <div className="text-xs opacity-75 mt-1">Perfect for trying new flavors</div>
            </div>
          </button>

          {hasSellingPlans && (
            <button
              type="button"
              onClick={() => setSelectedPurchaseType('subscription')}
              className={`p-3 rounded-xl border-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden ${
                selectedPurchaseType === 'subscription'
                  ? 'border-[#db8027] bg-gradient-to-br from-orange-100 to-yellow-50 text-[#db8027] shadow-lg ring-2 ring-[#db8027]/20'
                  : 'border-gray-200 bg-gradient-to-br from-white to-gray-50 text-gray-700 hover:border-[#db8027]/50 hover:shadow-md'
              }`}
            >
              {/* Animated background shimmer */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

              <div className="relative text-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 transition-all duration-300 ${
                  selectedPurchaseType === 'subscription'
                    ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f] shadow-md'
                    : 'bg-gradient-to-r from-gray-400 to-gray-500'
                }`}>
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="font-bold text-sm">Subscribe & Save</div>
                <div className="text-xs opacity-75 mt-1">First Month Free + 15% Off Every Order</div>
              </div>

              {/* Enhanced floating discount badge */}
              <div className="absolute -top-1 -right-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs px-2 py-0.5 rounded-full font-bold shadow-md animate-pulse">
                15% OFF
              </div>
            </button>
          )}
        </div>

        {/* Enhanced Subscription Frequency */}
        {selectedPurchaseType === 'subscription' && hasSellingPlans && (
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-3 border border-[#db8027]/30">
            {/* First Month Free Banner */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-2 mb-3 text-center">
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="font-bold text-sm">First Month Free Subscriptions</span>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>

            <div className="text-center mb-2">
              <h4 className="text-sm font-bold text-[#db8027] mb-1">🚚 Choose Your Delivery Schedule</h4>
            </div>

            <div className="grid grid-cols-2 gap-2">
              {subscriptionOptions.map((option) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setSelectedSellingPlanId(option.sellingPlanId)}
                  className={`p-2 rounded-md border transition-all duration-200 text-left ${
                    (selectedSellingPlanId || subscriptionOptions[1].sellingPlanId) === option.sellingPlanId
                      ? 'border-[#db8027] bg-white text-[#db8027] shadow-sm'
                      : 'border-gray-200 bg-white/50 text-gray-700 hover:border-[#db8027]/50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-xs">{option.name}</div>
                      <div className="text-xs opacity-75">{option.description}</div>
                    </div>
                    <div className="flex items-center">
                      {(selectedSellingPlanId || subscriptionOptions[1].sellingPlanId) === option.sellingPlanId && (
                        <svg className="w-4 h-4 text-[#db8027]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-2 text-center">
              <div className="inline-flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Free shipping on all subscription orders</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Quantity-Based Savings Buttons */}
      <div className="space-y-1.5 w-full">
        <div className="text-center">
          <h3 className="text-xs font-bold !text-black mb-0.5">Choose Your Quantity</h3>
        </div>

        <div className="flex flex-col space-y-1.5 w-full overflow-visible">
          {/* Single Item - Standard Option */}
          <div className="relative group w-full overflow-visible p-1 transform-none">
            <div className="w-full bg-gradient-to-r from-white to-gray-50 hover:from-gray-50 hover:to-gray-100 active:from-gray-100 active:to-gray-200 font-bold py-4 px-6 rounded-lg transition-[transform,shadow,background-color] duration-300 text-gray-800 text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-2 border-black cursor-pointer hover:ring-2 hover:ring-gray-300 hover:ring-opacity-50">
              <AddToCartButton
                disabled={!selectedVariant || !selectedVariant.availableForSale}
                onClick={() => open('cart')}
                lines={
                  selectedVariant
                    ? [
                        {
                          merchandiseId: selectedVariant.id,
                          quantity: 1,
                          selectedVariant,
                          ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                            sellingPlanId: selectedSellingPlanId,
                          }),
                        },
                      ]
                    : []
                }
                className="w-full h-full flex items-center justify-between bg-transparent border-none p-0 m-0 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <div className="bg-gray-800/20 rounded-full p-1">
                    <svg className="w-3 h-3 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-sm">Add 1 to cart</div>
                    <div className="text-xs opacity-90">Standard Price</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-base font-bold">
                    ${basePrice.toFixed(2)}
                  </div>
                  <div className="text-xs opacity-90">per bag</div>
                </div>
              </AddToCartButton>
            </div>
          </div>

          {/* 2 Items with 5% savings - Popular Choice */}
          <div className="relative group w-full overflow-visible p-1 transform-none">
            <div className="absolute -top-0.5 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-sm animate-pulse">
                💰 SAVE 5%
              </div>
            </div>
            <div className="w-full bg-gradient-to-r from-[#22c55e] to-[#16a34a] hover:from-[#16a34a] hover:to-[#15803d] active:from-[#15803d] active:to-[#166534] font-bold py-4 px-6 rounded-lg transition-[transform,shadow,background-color] duration-300 text-white text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-2 border-black cursor-pointer hover:ring-2 hover:ring-green-300 hover:ring-opacity-50">
              <AddToCartButton
                disabled={!selectedVariant || !selectedVariant.availableForSale}
                onClick={() => open('cart')}
                lines={
                  selectedVariant
                    ? [
                        {
                          merchandiseId: selectedVariant.id,
                          quantity: 2,
                          selectedVariant,
                          ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                            sellingPlanId: selectedSellingPlanId,
                          }),
                        },
                      ]
                    : []
                }
                className="w-full h-full flex items-center justify-between bg-transparent border-none p-0 m-0 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <div className="bg-white/20 rounded-full p-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-sm">Add 2 to cart</div>
                    <div className="text-xs opacity-90">Popular Choice</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-base font-bold">
                    ${(basePrice * 0.95).toFixed(2)}
                  </div>
                  <div className="text-xs opacity-90">per bag</div>
                  <div className="text-xs bg-white/20 rounded px-1 py-0.5 mt-0.5">
                    Save ${(basePrice * 0.05 * 2).toFixed(2)}
                  </div>
                </div>
              </AddToCartButton>
            </div>
          </div>

          {/* 3 Items with 10% savings - Best Value */}
          <div className="relative group w-full overflow-visible p-1 transform-none">
            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-sm">
                🔥 BEST VALUE - SAVE 10%
              </div>
            </div>
            <div className="w-full bg-gradient-to-r from-[#f97316] to-[#ea580c] hover:from-[#ea580c] hover:to-[#dc2626] active:from-[#dc2626] active:to-[#b91c1c] font-bold py-4 px-6 rounded-lg transition-[transform,shadow,background-color] duration-300 text-white text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-2 border-black cursor-pointer hover:ring-2 hover:ring-orange-300 hover:ring-opacity-50">
              <AddToCartButton
                disabled={!selectedVariant || !selectedVariant.availableForSale}
                onClick={() => open('cart')}
                lines={
                  selectedVariant
                    ? [
                        {
                          merchandiseId: selectedVariant.id,
                          quantity: 3,
                          selectedVariant,
                          ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                            sellingPlanId: selectedSellingPlanId,
                          }),
                        },
                      ]
                    : []
                }
                className="w-full h-full flex items-center justify-between bg-transparent border-none p-0 m-0 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <div className="bg-white/20 rounded-full p-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-sm">Add 3 to cart</div>
                    <div className="text-xs opacity-90">Best Value!</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-base font-bold">
                    ${(basePrice * 0.90).toFixed(2)}
                  </div>
                  <div className="text-xs opacity-90">per bag</div>
                  <div className="text-xs bg-white/20 rounded px-1 py-0.5 mt-0.5">
                    Save ${(basePrice * 0.10 * 3).toFixed(2)}
                  </div>
                </div>
              </AddToCartButton>
            </div>
          </div>
        </div>

        {/* Subscription Bonus Message */}
        {selectedPurchaseType === 'subscription' && (
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-[#db8027] rounded-lg p-2 text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <svg className="w-4 h-4 text-[#db8027]" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-bold text-[#db8027]">SUBSCRIPTION BONUS!</span>
            </div>
            <p className="text-[#db8027] font-medium text-xs">
              Stack your quantity savings with 15% subscription discount for maximum value!
            </p>
          </div>
        )}


      </div>
    </div>
  );
}



function ProductOptionSwatch({
  swatch,
  name,
}: {
  swatch?: Maybe<ProductOptionValueSwatch> | undefined;
  name: string;
}) {
  const image = swatch?.image?.previewImage?.url;
  const color = swatch?.color;

  if (!image && !color) return name;

  return (
    <div
      aria-label={name}
      className="product-option-label-swatch"
      style={{
        backgroundColor: color || 'transparent',
      }}
    >
      {!!image && <img src={image} alt={name} />}
    </div>
  );
}
