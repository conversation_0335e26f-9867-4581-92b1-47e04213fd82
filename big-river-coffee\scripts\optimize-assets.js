#!/usr/bin/env node

/**
 * Asset Optimization Script
 * Compresses stillframe images, re-compresses videos, and converts images to WebP
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';
import sharp from 'sharp';
import imagemin from 'imagemin';
import imageminPngquant from 'imagemin-pngquant';
import imageminMozjpeg from 'imagemin-mozjpeg';

const execAsync = promisify(exec);

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

// Configuration
const STILLFRAME_IMAGES = [
  'public/newhomepage/homepage_stillframe.png',
  'public/newhomepage/aboutus_stillframe.png',
  'public/newhomepage/shop_stillframe.png',
  'public/newhomepage/bg_video/bg_sf_new.png'
];

const IMAGES_TO_CONVERT = [
  // newhomepage folder images
  'public/newhomepage/Big_River_Coffee_Rectangle_Logo.png',
  'public/newhomepage/MELBlount_LOGO.png',
  'public/newhomepage/aboutus1.png',
  'public/newhomepage/aboutus2.png',
  'public/newhomepage/aboutus3.png',
  'public/newhomepage/figmass.png',
  'public/newhomepage/our_story.png',
  'public/newhomepage/shop_coffee.png',
  'public/newhomepage/social_media.png',
  'public/newhomepage/mobile_homeage_bg_sf.png',
  'public/newhomepage/bg_video/bg_sf_new.png',

  // K-cup product images
  'public/dark_colombian_kcup.png',
  'public/french_vanilla_kcup.png',
  'public/maple_bacon_kcup.png',
  'public/medium_nicaraguan_kcup.png',

  // Main public folder images that don't have WebP versions
  'public/brc.png',
  'public/our_story_wordart.png'
];

const VIDEOS_TO_COMPRESS = [
  'public/newhomepage/bg_video/bg_video_new.mp4',
  'public/newhomepage/aboutus_hero_video.mp4',
  'public/newhomepage/shop_hero_vid.mp4'
];

async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function getFileSize(filePath) {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch {
    return 0;
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function compressStillframes() {
  console.log('🖼️  Compressing stillframe images...');
  let totalSaved = 0;
  
  for (const imagePath of STILLFRAME_IMAGES) {
    const fullPath = path.join(projectRoot, imagePath);
    
    if (await fileExists(fullPath)) {
      const originalSize = await getFileSize(fullPath);
      console.log(`   Processing: ${path.basename(imagePath)}`);
      
      try {
        // Compress PNG with pngquant
        const compressed = await imagemin([fullPath], {
          plugins: [
            imageminPngquant({
              quality: [0.6, 0.8], // Good quality range for stillframes
              speed: 1 // Better compression
            })
          ]
        });
        
        if (compressed.length > 0) {
          await fs.writeFile(fullPath, compressed[0].data);
          const newSize = await getFileSize(fullPath);
          const saved = originalSize - newSize;
          totalSaved += saved;
          
          console.log(`     ✅ Compressed: ${formatBytes(originalSize)} → ${formatBytes(newSize)} (saved ${formatBytes(saved)})`);
        }
      } catch (error) {
        console.error(`     ❌ Error compressing ${imagePath}:`, error.message);
      }
    } else {
      console.log(`     ⚠️  File not found: ${imagePath}`);
    }
  }
  
  console.log(`💾 Total saved from stillframes: ${formatBytes(totalSaved)}\n`);
  return totalSaved;
}

async function convertToWebP() {
  console.log('🔄 Converting images to WebP format...');
  let totalConverted = 0;
  
  for (const imagePath of IMAGES_TO_CONVERT) {
    const fullPath = path.join(projectRoot, imagePath);
    
    if (await fileExists(fullPath)) {
      const originalSize = await getFileSize(fullPath);
      const webpPath = fullPath.replace(/\.(png|jpg|jpeg)$/i, '.webp');
      
      console.log(`   Converting: ${path.basename(imagePath)}`);
      
      try {
        await sharp(fullPath)
          .webp({ 
            quality: 85, // High quality for important images
            effort: 6   // Maximum compression effort
          })
          .toFile(webpPath);
        
        const webpSize = await getFileSize(webpPath);
        const saved = originalSize - webpSize;
        totalConverted++;
        
        console.log(`     ✅ Created WebP: ${formatBytes(originalSize)} → ${formatBytes(webpSize)} (saved ${formatBytes(saved)})`);
      } catch (error) {
        console.error(`     ❌ Error converting ${imagePath}:`, error.message);
      }
    } else {
      console.log(`     ⚠️  File not found: ${imagePath}`);
    }
  }
  
  console.log(`🎯 Converted ${totalConverted} images to WebP format\n`);
  return totalConverted;
}

async function compressVideos() {
  console.log('🎬 Compressing video files...');
  let totalCompressed = 0;
  let totalSaved = 0;

  for (const videoPath of VIDEOS_TO_COMPRESS) {
    const fullPath = path.join(projectRoot, videoPath);

    if (await fileExists(fullPath)) {
      const originalSize = await getFileSize(fullPath);
      const compressedPath = fullPath.replace('.mp4', '_compressed.mp4');

      console.log(`   Compressing: ${path.basename(videoPath)}`);

      try {
        // Use FFmpeg to compress video with good quality/size balance
        const ffmpegCommand = `ffmpeg -i "${fullPath}" -c:v libx264 -crf 28 -preset medium -c:a aac -b:a 128k -movflags +faststart -y "${compressedPath}"`;

        await execAsync(ffmpegCommand);

        const compressedSize = await getFileSize(compressedPath);
        const saved = originalSize - compressedSize;
        totalSaved += saved;
        totalCompressed++;

        console.log(`     ✅ Compressed: ${formatBytes(originalSize)} → ${formatBytes(compressedSize)} (saved ${formatBytes(saved)})`);

        // Replace original with compressed version
        await fs.rename(compressedPath, fullPath);
        console.log(`     🔄 Replaced original with compressed version`);

      } catch (error) {
        console.error(`     ❌ Error compressing ${videoPath}:`, error.message);
      }
    } else {
      console.log(`     ⚠️  File not found: ${videoPath}`);
    }
  }

  console.log(`🎬 Compressed ${totalCompressed} videos, saved ${formatBytes(totalSaved)}\n`);
  return { totalCompressed, totalSaved };
}

// Main execution function
async function main() {
  console.log('🚀 Starting Big River Coffee Asset Optimization...\n');

  try {
    // Step 1: Compress stillframe images
    const savedFromStillframes = await compressStillframes();

    // Step 2: Convert images to WebP
    const convertedImages = await convertToWebP();

    // Step 3: Compress videos
    const { totalCompressed: compressedVideos, totalSaved: savedFromVideos } = await compressVideos();

    console.log('✅ Asset optimization completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Stillframes optimized with ${formatBytes(savedFromStillframes)} saved`);
    console.log(`   • ${convertedImages} images converted to WebP format`);
    console.log(`   • ${compressedVideos} videos compressed with ${formatBytes(savedFromVideos)} saved`);
    console.log('\n🎯 Next steps:');
    console.log('   1. Update OptimizedVideo components to use stillframes');
    console.log('   2. Test video loading performance');
    console.log('   3. Deploy optimized assets to production\n');

  } catch (error) {
    console.error('❌ Error during optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
