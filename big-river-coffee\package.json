{"name": "big-river-coffee", "private": true, "sideEffects": false, "version": "2025.5.1", "type": "module", "scripts": {"build": "shopify hydrogen build --codegen", "dev": "shopify hydrogen dev --codegen", "preview": "shopify hydrogen preview --build", "lint": "eslint --no-error-on-unmatched-pattern .", "typecheck": "tsc --noEmit", "codegen": "shopify hydrogen codegen"}, "prettier": "@shopify/prettier-config", "dependencies": {"@shopify/admin-api-client": "^1.0.8", "@shopify/hydrogen": "2025.5.0", "@shopify/remix-oxygen": "^3.0.0", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "isbot": "^5.1.22", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "7.6.0", "react-router-dom": "7.6.0", "sharp": "^0.34.2", "tailwindcss": "^4.1.6"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@graphql-codegen/cli": "5.0.2", "@react-router/dev": "7.6.0", "@react-router/fs-routes": "7.6.0", "@shopify/cli": "~3.80.4", "@shopify/hydrogen-codegen": "^0.3.3", "@shopify/mini-oxygen": "^3.2.1", "@shopify/oxygen-workers-types": "^4.1.6", "@shopify/prettier-config": "^1.1.2", "@tailwindcss/vite": "^4.1.6", "@total-typescript/ts-reset": "^0.6.1", "@types/eslint": "^9.6.1", "@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.14.0", "imagemin": "^9.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "prettier": "^3.4.2", "typescript": "^5.2.2", "vite": "^6.2.4", "vite-tsconfig-paths": "^4.3.1"}, "engines": {"node": ">=18.0.0"}, "browserslist": ["defaults"]}