import{w as i}from"./with-props-CE_bzIRz.js";import{j as t}from"./jsx-runtime-CWqDQG74.js";import{u as c,L as r}from"./chunk-D4RADZKF-CZTShXQu.js";import{P as a}from"./PaginatedResourceSection-CD6twqqN.js";import{f as l}from"./index-De_rdhZz.js";import{M as d}from"./Money-St0DOtgu.js";const b=()=>[{title:"Orders"}],O=i(function(){const{customer:s}=c(),{orders:e}=s;return t.jsx("div",{className:"orders",children:e.nodes.length?t.jsx(u,{orders:e}):t.jsx(o,{})})});function u({orders:n}){return t.jsx("div",{className:"acccount-orders",children:n!=null&&n.nodes.length?t.jsx(a,{connection:n,children:({node:s})=>t.jsx(x,{order:s},s.id)}):t.jsx(o,{})})}function o(){return t.jsxs("div",{children:[t.jsx("p",{children:"You haven't placed any orders yet."}),t.jsx("br",{}),t.jsx("p",{children:t.jsx(r,{to:"/collections/all",children:"Start Shopping →"})})]})}function x({order:n}){var e;const s=(e=l(n.fulfillments)[0])==null?void 0:e.status;return t.jsxs(t.Fragment,{children:[t.jsxs("fieldset",{children:[t.jsx(r,{to:`/account/orders/${btoa(n.id)}`,children:t.jsxs("strong",{children:["#",n.number]})}),t.jsx("p",{children:new Date(n.processedAt).toDateString()}),t.jsx("p",{children:n.financialStatus}),s&&t.jsx("p",{children:s}),t.jsx(d,{data:n.totalPrice}),t.jsx(r,{to:`/account/orders/${btoa(n.id)}`,children:"View Order →"})]}),t.jsx("br",{})]})}export{O as default,b as meta};
