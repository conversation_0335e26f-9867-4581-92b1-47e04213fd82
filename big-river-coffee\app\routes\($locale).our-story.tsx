import { type MetaFunction } from 'react-router';
import { useEffect, useState } from 'react';
import {OptimizedVideo} from '~/components/OptimizedVideo';
import {initScrollAnimations} from '~/lib/scrollAnimations';

export const meta: MetaFunction = () => {
  return [
    { title: 'Our Story | Big River Coffee' },
    { description: 'Big River Coffee is more than just a coffee brand; it\'s an experience that combines the rugged charm of the Outdoors with the rich and delightful flavors of Latin American coffees.' }
  ];
};

export default function OurStoryPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add body class for page-specific styling
    document.body.classList.add('our-story');

    // Ensure videos play properly
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.play().catch(error => {
        console.log('Video autoplay prevented:', error);
      });
    });

    return () => {
      document.body.classList.remove('our-story');
    };
  }, []);

  // Initialize scroll animations
  useEffect(() => {
    const cleanup = initScrollAnimations();
    return cleanup;
  }, []);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <>
    <style>{`
      body.our-story {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.our-story main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

      {/* Hero Video Section - Desktop Only */}
      <div className="hidden sm:block sticky top-0 w-full h-screen z-0">
        {/* Desktop Hero Video */}
        <div className="w-full h-full">
          <OptimizedVideo
            src="/newhomepage/aboutus_hero_video.mp4"
            poster="/newhomepage/aboutus_stillframe.webp"
            fallbackImage="/newhomepage/aboutus_stillframe.png"
            className="w-full h-full object-cover"
            style={{
              width: '100%',
              height: '100%',
              display: 'block'
            }}
            autoPlay={true}
            muted={true}
            loop={true}
            playsInline={true}
            preload="metadata"
            lazy={false}
            forceMobileImage={true}
            onLoadStart={() => console.log('Video loading started')}
            onCanPlay={() => console.log('Video can play')}
            onError={() => console.error('Video error')}
          />
        </div>
      </div>

      {/* Story Content Section */}
      <div className="relative z-10 min-h-screen border-t-8 border-army-600" style={{ backgroundColor: '#eeedc1', marginTop: '0' }}>

        {/* Hero Title Section */}
        <div className="py-20 relative overflow-hidden scroll-fade-in">
          {/* Background decorative circles */}
          <div className="absolute top-10 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute top-20 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>

          <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
            <h1 className="text-5xl lg:text-6xl xl:text-7xl font-bold text-black mb-6" style={{ fontFamily: 'var(--font-title)' }}>
              Our Story
            </h1>
            <div className="w-24 h-1 bg-army-600 mx-auto mb-8"></div>
            <p className="text-xl lg:text-2xl text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
              Discover the journey behind every cup of Big River Coffee
            </p>
          </div>
        </div>

        {/* About Us Section 1 */}
        <div className="py-16 relative overflow-hidden scroll-fade-in delay-100">
          {/* Decorative circles */}
          <div className="absolute top-8 left-8 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute bottom-12 right-12 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
          <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-army-600 rounded-full opacity-18"></div>

          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-r-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-4xl lg:text-5xl font-bold text-black mb-8" style={{ fontFamily: 'var(--font-title)' }}>
                  Big River Coffee
                </h2>
                <p className="text-lg text-black leading-relaxed mb-6" style={{ fontFamily: 'var(--font-body)' }}>
                  Big River Coffee is more than just a coffee brand; it's an experience that combines the rugged charm of the Outdoors with the rich and delightful flavors of Latin American coffees.
                </p>
                <p className="text-lg text-black leading-relaxed mb-6" style={{ fontFamily: 'var(--font-body)' }}>
                  As part of the Good Coffee Co conglomerate, we are driven by the belief that coffee should not only taste exceptional but should also be a catalyst for positive change.
                </p>
                <p className="text-xl font-semibold text-army-200 bg-army-600 px-4 py-2 rounded-lg inline-block" style={{ fontFamily: 'var(--font-header)' }}>
                  Thanks for being a part of the Big River Coffee family!
                </p>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <picture>
                    <source srcSet="/newhomepage/aboutus1.webp" type="image/webp" />
                    <img
                      src="/newhomepage/aboutus1.png"
                      alt="Big River Coffee story"
                      className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                    />
                  </picture>
                  <div className="absolute top-4 right-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>


        {/* About Us Section 2 */}
        <div className="py-16 relative overflow-hidden scroll-fade-in delay-200">
          {/* Decorative circles */}
          <div className="absolute top-12 right-8 w-24 h-24 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-8 left-16 w-32 h-32 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute top-1/4 left-8 w-14 h-14 bg-army-600 rounded-full opacity-25"></div>
          <div className="absolute bottom-1/3 right-1/4 w-18 h-18 bg-army-600 rounded-full opacity-22"></div>

          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-l-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <picture>
                    <source srcSet="/newhomepage/aboutus2.webp" type="image/webp" />
                    <img
                      src="/newhomepage/aboutus2.png"
                      alt="Contributing to the common good"
                      className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                    />
                  </picture>
                  <div className="absolute top-4 left-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
              <div>
                <h2 className="text-4xl lg:text-5xl font-bold text-black mb-8" style={{ fontFamily: 'var(--font-title)' }}>
                  Contributing to the Common Good
                </h2>
                <div className="w-16 h-1 bg-army-600 mb-6"></div>
                <p className="text-lg text-black leading-relaxed mb-6" style={{ fontFamily: 'var(--font-body)' }}>
                  Our contribution is like a river that flows from the origin to the morning table of our customers—a coffee loaded with values and perfectly traceable.
                </p>
                <p className="text-lg text-black leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                  Through Big River Coffee, you can discover the adventures that coffee families have lived for the last 30 years, all to keep alive the tradition, culture, and passion for coffee.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* About Us Section 3 */}
        <div className="py-16 relative overflow-hidden scroll-fade-in delay-300">
          {/* Decorative circles */}
          <div className="absolute top-16 left-12 w-26 h-26 bg-army-600 rounded-full opacity-18"></div>
          <div className="absolute bottom-16 right-8 w-22 h-22 bg-army-600 rounded-full opacity-28"></div>
          <div className="absolute top-1/3 right-1/3 w-10 h-10 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-1/4 left-1/4 w-14 h-14 bg-army-600 rounded-full opacity-16"></div>
          <div className="absolute top-8 right-16 w-8 h-8 bg-army-600 rounded-full opacity-30"></div>

          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-r-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-4xl lg:text-5xl font-bold text-black mb-8" style={{ fontFamily: 'var(--font-title)' }}>
                  The Freedom to Help
                </h2>
                <div className="w-16 h-1 bg-army-600 mb-6"></div>
                <p className="text-lg text-black leading-relaxed mb-6" style={{ fontFamily: 'var(--font-body)' }}>
                  It's like morning landscapes or a mountain peak—every time you shake someone's hand to achieve that long-awaited peak, the same thing happens when you buy or subscribe to Big River Coffee.
                </p>
                <p className="text-lg text-black leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                  This same feeling of freedom and genuine help carries over to the beautiful mountains from which our coffee is selected. Our special connections with Nicaragua have made us work closely with the Good Coffee Beans Cooperative to obtain high-quality beans grown at more than 5,000 feet above sea level.
                </p>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <picture>
                    <source srcSet="/newhomepage/aboutus3.webp" type="image/webp" />
                    <img
                      src="/newhomepage/aboutus3.png"
                      alt="The Freedom to Help"
                      className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                    />
                  </picture>
                  <div className="absolute bottom-4 right-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-20 bg-gradient-to-r from-amber-500 to-orange-500 relative overflow-hidden scroll-fade-in delay-400">
          {/* Decorative circles */}
          <div className="absolute top-8 left-8 w-20 h-20 bg-white rounded-full opacity-10"></div>
          <div className="absolute bottom-8 right-8 w-24 h-24 bg-white rounded-full opacity-15"></div>
          <div className="absolute top-1/2 left-16 w-12 h-12 bg-white rounded-full opacity-20"></div>
          <div className="absolute bottom-16 left-1/3 w-16 h-16 bg-white rounded-full opacity-12"></div>
          <div className="absolute top-12 right-1/4 w-14 h-14 bg-white rounded-full opacity-18"></div>

          <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
            <h2 className="text-4xl lg:text-5xl font-bold text-black mb-8" style={{ fontFamily: 'var(--font-title)' }}>
              Ready to Taste the Adventure?
            </h2>
            <div className="mb-12 flex justify-center">
              <p className="text-xl text-black/90 max-w-2xl leading-relaxed text-center" style={{ fontFamily: 'var(--font-body)' }}>
                Join thousands of coffee lovers who have discovered the perfect blend of adventure and exceptional taste. Every cup supports the communities that make it possible.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <a
                href="/collections/all"
                className="inline-flex items-center px-8 py-4 bg-white text-amber-600 rounded-xl font-semibold text-lg hover:bg-gray-50 hover:scale-105 transition-all duration-300 shadow-lg"
                style={{ fontFamily: 'var(--font-header)' }}
              >
                <span>Shop Our Coffee</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
              <a
                href="/collections/all?section=subscriptions"
                className="inline-flex items-center px-8 py-4 bg-army-600 text-white rounded-xl font-semibold text-lg hover:bg-army-700 hover:scale-105 transition-all duration-300 shadow-lg"
                style={{ fontFamily: 'var(--font-header)' }}
              >
                <span>Start Subscription</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </a>
            </div>
          </div>
        </div>



        {/* FAQ Section */}
        <div className="py-20 relative overflow-hidden scroll-fade-in delay-500">
          {/* Decorative circles */}
          <div className="absolute top-12 left-8 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute bottom-16 right-12 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
          <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-army-600 rounded-full opacity-18"></div>

          <div className="max-w-4xl mx-auto px-6 relative z-10">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-black mb-6" style={{ fontFamily: 'var(--font-title)' }}>
                Frequently Asked Questions
              </h2>
              <div className="w-24 h-1 bg-army-600 mx-auto mb-6"></div>
              <p className="text-xl text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                Everything you need to know about Big River Coffee
              </p>
            </div>

            {/* FAQ Items */}
            <div className="space-y-4">
              {/* FAQ 1 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(1)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    What makes Big River Coffee different?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 1 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 1 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      Big River Coffee is mold-free, shade-grown, and directly sourced from Nicaragua's finest farms. We skip the middlemen so your cup is fresher, smoother, and full of clean energy. No regrets—just bold, wild flavor.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 2 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(2)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    What is shade-grown coffee and why does it matter?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 2 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 2 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      Shade-grown coffee is cultivated under natural forest canopies, which protects biodiversity and results in slower, richer bean development. That means a smoother taste and a happier planet.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 3 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(3)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Is Big River Coffee mold-free?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 3 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 3 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      Yes! Our beans are rigorously tested and processed to ensure they're free from mold and mycotoxins. Clean coffee = clean energy = clean vibes.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 4 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(4)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Is your coffee low-acid?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 4 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 4 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      Definitely. Our beans are naturally lower in acidity, making them gentler on the stomach while still bold in flavor. Perfect for early risers and sensitive sippers alike.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 5 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(5)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Where do you source your beans from?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 5 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 5 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      We work directly with small-scale farmers in the wild highlands of Nicaragua, ensuring ethical practices, better quality, and fresher coffee straight from origin.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 6 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(6)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Is your packaging eco-friendly?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 6 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 6 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      Yes! We use minimal, recyclable, and compostable packaging that aligns with our commitment to sustainability. Great coffee shouldn't come at the planet's expense.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 7 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(7)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Do you offer subscriptions?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 7 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 7 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      YES! We offer a{' '}
                      <a
                        href="/collections/all?section=subscriptions"
                        className="text-army-600 hover:text-army-700 font-semibold underline"
                      >
                        wide selection of subscription products
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 8 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(8)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    How should I brew Big River Coffee?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 8 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 8 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <p className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      <a
                        href="/pages/brew"
                        className="text-army-600 hover:text-army-700 font-semibold underline"
                      >
                        Follow this link to find your brew!
                      </a>{' '}
                      We have detailed guides for pour-over, French press, AeroPress, espresso, cold brew, and more.
                    </p>
                  </div>
                </div>
              </div>

              {/* FAQ 9 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(9)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-black" style={{ fontFamily: 'var(--font-header)' }}>
                    Where can I try Big River Coffee in person?
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <div className={`w-6 h-6 rounded-full bg-army-600 flex items-center justify-center transition-transform duration-200 ${openFAQ === 9 ? 'rotate-45' : ''}`}>
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQ === 9 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="px-6 pb-5">
                    <div className="text-gray-700 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                      <p className="mb-4">
                        We host weekly sampling events at local markets and stores across Pittsburgh and nearby towns, including:
                      </p>
                      <ul className="list-disc pl-6 mb-4 space-y-1">
                        <li>Pure Barre Shadyside</li>
                        <li>Fresh Thyme (Bridgeville, Cranberry, Pleasant Hills)</li>
                        <li>Harvest Valley in Bakerstown</li>
                        <li>Kunz Markets (McKnight Rd & Allison Park)</li>
                        <li>Cranberry Farmers Market</li>
                      </ul>
                      <p>
                        ☕ Swing by to taste the wild. 📍Follow{' '}
                        <a
                          href="https://instagram.com/bigriver.coffee"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-army-600 hover:text-army-700 font-semibold underline"
                        >
                          @bigriver.coffee
                        </a>{' '}
                        for updated times and locations each week!
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
